{"labels": {"auth": {"backToLogin": "Back to log in", "changePassword": {"changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "retypeNewPassword": "Retype New Password"}, "createAccount": "Get in touch", "downloadForAndroid": "Download for Android", "downloadForIos": "Download for iOS", "downloadTheApp": "Download the app", "forgotPassword": {"checkYourEmail": "Check your email for instructions from us on how to reset your password.", "emailSent": "Email sent", "enterYourEmail": "Input your email, and we’ll send you a link to reset your password.", "forgotYourPassword": "Forgot Your Password", "formInputPlaceholder": "Input your email", "formLabel": "Please enter your email to reset your Password", "passwordEmailSentMessage": "We have sent you an email with instructions to reset your password.", "requestNewPassword": "Request New Password", "resendEmail": "Resend email", "sendResetLink": "Send reset link", "submitButtonLabel": "Continue"}, "login": "<PERSON><PERSON>", "loginForm": {"connect": "Connect", "connectWithPrism": "Connect with prism", "contactUs": "To register your interest in <PERSON><PERSON>,<br></br> please contact us <link>here</link>", "forgotPassword": "Forgot Password", "havingTroubleLoggingIn": "Having trouble logging in?", "logIntoPrism": "Login to Prism", "login": "Log in", "prismDeviceSuportMessage": "Use Prism on your desktop, tablet or mobile.", "tryPrismForFree": "Don't have an account? Try Prism for free.", "userLogin": "User login"}, "logout": "Logout", "resetPassword": {"enterYourNewPassword": "Input your new password", "invalidMessage": "Uh-oh!", "passwordChangedSuccess": "Your password has been changed.", "reset": "Reset", "resetLinkInvalidMessage": "It looks like this reset password link has expired or has already been used.", "resetYourPassword": "Reset your password"}}, "breeding": {"addBooking": "Add Booking", "addCommissionFee": "Add Commission Fee", "addServiceFee": "Add Service Fee", "agent": "Agent", "bookings": {"addNewOwner": "Add New Owner", "addOwnership": "Add Ownership", "agent": "Agent", "agentCommission": "Agent Commission", "appointment": "Appointment", "barn": "<PERSON>n", "bookingDate": "Booking Date", "bookingStatus": "Booking Status", "category": "Category", "changeBookingCost": "Due to service fee on owners has been changed you should update manually for existing service fee charge to owners for this booking", "chargeServiceFeeTo": "Charge Service Fee To", "commission": "Commission", "confirmDeleteBooking": "Are you sure you want to delete this booking?", "confirmDeleteConfirmBooking": "The booking has been executed. Are you sure you want to continue?", "confirmDeleteServiceFeeBooking": "Do you want to remove the transactions and the contract associated to this booking?", "contractReceived": "Contract Received", "contractSent": "Contract Sent", "country": "Country", "coverDate": "Cover Date", "createContract": "Do you want to create a Contract for this booking?", "discount": "Discount", "discountedFee": "Discounted Fee", "editBreedingTasks": "Edit Breeding Tasks", "editOwnership": "Edit Ownership", "foal": "Foal", "foalDue": "Foal Due", "inputDiscountedFee": "Input Discounted <PERSON>e", "inputNote": "Input Note", "inputServiceFee": "Input Service Fee", "inputStallionName": "Input Stallion Name", "lastStatus": "Last Status", "location": "Location", "mare": "Mare", "mareOwnership": "Mare Ownership", "mareTBC": "Mare TBC", "name": "Name", "note": "Note", "outsideStallion": "Outside Stallion", "owner": "Owner", "ownerList": "Owner List", "ownerShare": "Owner Share", "payableCommissionToTheBookingAgent": "Payable commission to the Booking Agent.", "report": "Report", "scan1": "Scan 1", "scan2": "Scan 2", "scan3": "Scan 3", "season": "Season", "selectAgent": "Select Agent", "selectAppointment": "Select Appointment", "selectBarn": "Select Barn", "selectCategory": "Select Category", "selectContractReceived": "Select Contract Received", "selectContractSent": "Select Contract Sent", "selectCountry": "Select Country", "selectLocation": "Select Location", "selectOwner": "Select Owner", "selectReport": "Select Report", "selectStallion": "Select Stallion", "selectStallionFeeCategory": "Select Stallion Fee Category", "selectStatus": "Select Status", "selectTime": "Select Time", "service": "Service", "serviceFee": "Service Fee", "serviceFeePayable": "Service Fee Payable", "shareholding": "Shareholding", "source": "Source", "stallion": "Stallion", "stallionBooking": "Stallion Booking", "stallionFeeCategory": "Stallion Fee Category", "stallionType": "Stallion Type", "standingStallion": "Standing Stallion", "status": "Status", "time": "Time", "totalMoney": "Total Money", "viewOwnerList": "Click to view Owner List", "waringCreditNote": "Please note that the total amount being charged to the Owner(s) is less than the Service Fee preset. Please confirm that you wish to continue?", "waringNoOwner": "The mare has no owners to take the cost so the service fee will not be applied for it. Are you sure you want to continue?", "warningExisted": "You have already added booking for {stallionName} with {mareName} on this date. Do you wish to continue?"}, "commissionFeeAdded": "The commission fee has been added to transaction list", "commissionFeeDescription": "Commission Fee for {stallionName} - {mareName} - {season}", "dashboard": {"bookingRequest": "Booking Request", "bookings": "Bookings", "immediaServiceRequest": "Immedia Service Request"}, "date": "Date", "description": "Description", "editBooking": "Edit Booking", "inputDescription": "Input Description", "inputInvoiceMessage": "Input Invoice Message", "invoiceMessage": "Invoice Message", "mare": "Mare", "nav": {"dashboard": "Dashboard", "foalsReport": "Foals Report", "mareCriteria": "Mare Criteria", "mareNoBooking": "Mare No Booking", "serviceFee": "Service Fee", "serviceFeeReport": "Service Fee Report", "stallionContracts": "Stallion Contracts", "stallionRoster": "Stallion Roster", "weeklyStats": "Weekly Stats"}, "purchasesDescription": "Purchases Description", "selectAgent": "Select Agent", "task": {"actualDate": "Actual Date", "bookedTo": "Booked To", "category": "Category", "cervix": "Cervix", "description": "Description", "fluid": "Fluid", "foldsOedema": "Folds/<PERSON>edema", "inputNote": "Input Note", "inputResult": "Input Result", "leftOvary": "Left ovary", "mareName": "Mare Name", "note": "Note", "noteForTask": "Note For Task", "result": "Result", "rightOvary": "Right ovary", "scheduleDate": "Schedule Date", "season": "Season", "selectDate": "Select Date", "selectResult": "Select Result", "selectSeason": "Select Season", "selectTaskType": "Select Task Type", "selectTime": "Select Time", "stallionName": "Stallion Name", "taskType": "Task Type", "time": "Time", "uterus": "Uterus"}, "serviceFeeReport": {"totalInvoice": "Total Invoice", "totalPaid": "Total Paid", "totalCredit": "Total Credit", "totalOutstanding": "Total Outstanding", "table": {"season": "Season", "owner": "Owner", "stallion": "Stallion", "mare": "Mare", "serviceFee": "Service Fee", "invoiceDate": "Invoice Date", "totalInvoice": "Total Invoice", "paid": "Paid", "credit": "Credit", "outstanding": "Outstanding", "lastPayment": "Last Payment"}}}, "cms": {"bannerImage": {"addBanner": "Add Banner", "confirmDelete": "Are you sure you want to delete this banner?", "createBannerImageSuccess": "Banner added successfully.", "cropImage": "Crop image", "delete": "Delete", "deleteSuccess": "Banner deleted successfully!", "editBanner": "Edit Banner", "editBannerImageSuccess": "Banner updated successfully.", "image": "Image", "inputLink": "Input Link", "inputTitleLine1": "Input Title Line 1", "inputTitleLine2": "Input Title Line 2", "link": "Link", "media": "Media", "orderBannerImageSuccess": "Banner reordered successfully.", "saveOrders": "Save Orders", "stable": "Stable", "title": "Title", "titleLine1": "Title Line 1", "titleLine2": "Title Line 2"}, "bannerVideo": {"addMedia": "Add Media", "addVideo": "Add Video", "createSuccess": "Video successfully uploaded.", "deleteMediaConfirm": "Are you sure you want to delete this {value}", "description": "Description", "editSuccess": "Video successfully edited.", "image": "Image", "title": "Title", "video": "Video"}, "customField": {"addCustomField": "Add Custom Field", "addSuccess": "Add Custom Field successfully", "confirmDelete": "Are you sure you want to delete this custom field?", "content": "Content", "customField": "Custom Field", "delete": "Delete", "deleteStableSuccess": "Delete stable custom field success !", "edit": "Edit", "inputLink": "Input Link", "inputName": "Input Name", "inputTitle": "Input Title", "link": "Link", "pageContent": "Page Content", "pageLocation": "Page Location", "title": "Title", "updatesuccessfully": "Update Custom Field successfully"}, "events": {"addEvent": "Add Event", "addSuccess": "Event added successfully", "cmsEvent": "CMS Event", "confirmDelete": "Are you sure you want to delete this Event?", "dateAndTime": "Date and Time", "deleteSuccess": "Event delete successfully", "description": "Description", "edit": "Edit", "editEvent": "Edit Event", "eventName": "Event Name", "expired": "Expired", "inputEventName": "Input Event Name", "inputTitle": "Input Location", "location": "Location", "media": "Media", "preview": "Preview", "previewEvent": "Preview Event", "startDate": "Start Date", "title": "Title", "updateSuccess": "Event edited successfully"}, "floatButton": {"banners": "Banners", "cmsStaff": "CMS Staff", "cmsVideos": "CMS Videos", "currentHorse": "Current Horse", "honourRoll": "Honour Roll", "honourRollsV": "Honour Rolls (V)", "horseSale": "Horse Sale", "photoGallery": "Photo Gallery", "webArticle": "Web Article"}, "honourRoll": {"addHonourRoll": "Add Honour Roll", "addLink": "Add Link Honour Roll", "addMoreVideo": "Add More Video", "addRace": "Add race", "addSuccess": "Honour Roll added successfully.", "confirmDelete": "Are you sure you want to delete this honour roll?", "deleteSuccess": "Honour roll deleted.", "description": "Description", "earnings": "Earnings", "editHonourRoll": "Edit Honour Roll", "editLink": "Edit Link Honour Roll", "editSuccess": "Honour Roll updated successfully.", "horseMedia": "Horse Media", "horseName": "Horse Name", "insertLink": "Insert Url Link", "mediaDate": "Media Date", "orderHonourRoll": "Order Honour Roll", "previewHonourRoll": "Preview Honour Roll", "salePrice": "Sale Price", "videoDescription": "Video Description", "videoLink": "Video Link"}, "horseOwnership": {"addNew": "Add New", "addOwnership": "Add Ownership", "confirmDeleteMsg": "Are you sure you want to remove this horse ownership?", "deleteSuccessMsg": "Horse Ownership is successfully removed.", "horse": "Horse", "horseName": "Horse Name", "horseOwnership": "Horse Ownership", "owners": "Owners", "selectHorse": "Select Horse", "updateSuccessMsg": "Horse Ownership is updated successfully"}, "job": {"action": {"addNewJob": "Add New Job", "editJob": "Edit Job"}, "content": "Content", "expired": "Expired", "images": "Images", "input": {"placeHolder": {"enterTitle": "Enter Title", "inputPublishedDate": "Input Published Date"}}, "message": {"jobAddedSucces": "Job added successfully", "jobDeleteFailed": "Job delete failed", "jobDeleteSuccess": "Job delete successfully", "jobEditSuccess": "<PERSON> edited successfully", "warningDeleteMessage": "Are you sure you want to delete this Job?"}, "publishedDate": "Published Date", "title": "Title"}, "nav": {"bannerImage": "Banners Image", "bannerVideo": "Banners Video", "currentHorse": "Current Horse", "customField": "Custom Field", "event": "Event", "honourRoll": "Honour Roll", "honourRollV": "Honour Roll (V)", "horseOwnership": "Horse Ownership", "job": "Job", "photoGallery": "Photo Gallery", "staff": "Staff", "testimonial": "Testimonial", "verifyIg": "Verify IG Feed", "videos": "Videos", "webArticle": "Web Article", "webConfiguration": "Web Configuration", "websiteStats": "Website Stats", "winnerImage": "Winner Image"}, "news": {"addNews": "Add News", "confirmDelete": "Are you sure you want to delete this news?", "content": "Content", "createNewsSuccess": "Article created successfully.", "crmOwnerPortalOnly": "CMR Owner Portal Only", "deleteNew": "Delete News", "deleteSuccess": "Article deleted successfully.", "editNews": "Edit", "editNewsSuccess": "Article updated successfully.", "id": "ID", "images": "Images", "inputContent": "Input Content", "inputTitle": "Input Title", "media": "Media", "previewNews": "Preview News", "publishedDate": "Published Date", "title": "Title"}, "photoGallery": {"addImage": "Add Image", "addMedia": "Add Media", "createSuccess": "Photo successfully uploaded.", "description": "Description", "editSuccess": "{value} successfully edited.", "insertDescription": "Insert Description", "insertTitle": "Insert Title", "title": "Title"}, "raceMedia": {"updateSuccessMsg": "Winner image updated successfully.", "updateWinnerImage": "Update Winner Image"}, "staff": {"action": {"addNewStaff": "Add New Staff", "editStaff": "Edit Staff", "previewStaff": "Preview Staff Profile"}, "avatar": "Avatar", "input": {"label": {"description": "Description", "email": "Email", "image": "Image", "phone": "Phone", "position": "Position", "stable": "Stable", "staffName": "Staff Name", "trainer": "Trainer"}, "placeholder": {"enterEmail": "Input Email", "enterPhone": "Input Phone", "enterPosition": "Input Position", "inputStaff": "Input Staff"}}, "message": {"deleteStaffError": "Staff delete error", "deleteStaffSuccess": "Staff deleted", "staffUpdatedSuccessfully": "Staff updated successfully.", "unableUpdateStaffPosition": "Unable update staff position!", "warningDeleteStaff": "Are you sure you want to delete this staff?"}}, "testimonial": {"action": {"addNewTestimonial": "Add New Testimonial", "editTestimonial": "Edit Testimonial"}, "avatar": "Avatar", "description": "Description", "input": {"placeHolder": {"enterPosition": "Enter Position", "enterTestimonialName": "Enter Testimonial Name"}}, "message": {"deleteTestimonial": "Testimonial deleted.", "testimonialAddSuccessful": "Testimonial added successfully", "testimonialUpdateSuccessful": "Testimonial updated successfully", "warningDeleteTestimonial": "Are you sure you want to delete this testimonial?"}, "position": "Position", "testimonialName": "Testimonial Name", "trainerName": "Trainer Name"}, "websiteConfig": {"Colours": "Colours", "alternativeLogo": "Alternative Logo", "contactInfo": "Contact Info", "facebooklink": "Facebook link", "inputPrimaryColour": "Input primary colour (Hex)", "inputSecondaryColour": "Input secondary colour (Hex)", "instagramLink": "Instagram link", "linkedInLink": "LinkedIn link", "logo": "Logo", "primaryLogo": "Primary Logo", "socialFeed": "Social Feed", "socialLinks": "Social Links", "twitterLink": "Twitter link", "vimeoLink": "Vimeo Link", "webConfiguration": "Web Configuration", "youtubeLink": "Youtube link"}, "websiteStats": {"addNewWebsiteStats": "Add New Website Stats", "addWebsiteStats": "Add Website Stats", "createdSuccessMsg": "Website Stats created successfully.", "deleteConfirmMsg": "Are you sure you want to delete this Website Stats?", "deleteSuccessMsg": "Website Stats has been deleted successfully.", "description": "Description", "earnings": "Earnings", "editWebsiteStats": "Edit Website Stats", "fifth": "5th", "first": "1st", "fourth": "4th", "metroPerformedStrikeRate": "Metro performed strike rate", "placeOrTop3": "Place/Top 3", "placeOrTop3Percent": "Place/Top 3 %", "prizeMoney": "Prize Money", "season": "Season", "second": "2nd", "stats": "Stats", "strikeRate": "Strike Rate", "third": "3rd", "updatedSuccessMsg": "Website Stats updated successfully.", "win": "Win", "winPercent": "Win %"}}, "common": {"APIKey": "API Key", "accept": "Accept", "add": "Add", "addDayRate": "Add day rate", "addHorseReport": "Add Horse Report", "addImage": "Add image", "addMedia": "Add Media", "addNew": "Add new", "addNote": "Add Note", "addProcedure": "Add Procedure", "addSuccess": "{value} successfully", "addSuccessPublish": "{value} successfully published", "addTitle": "Add {value}", "addVideo": "Add video", "ageSex": "Age/Sex", "all": "All", "allHorses": "All Horses", "allItem": "All Items", "allSelect": "Select All", "allTasks": "All tasks", "apiKey": "API Key", "apply": "Apply", "applyAll": "Apply All", "arrivalAdviceEmail": "Arrival Advice Email", "arrivalNotifications": "Arrival Notifications", "assignedTo": "Assigned To", "attach": "Attach", "attachFile": "Attach File", "attachProcedure": "Attach Procedure", "attachment": "Attachment", "attachments": "Attachments", "back": "Back", "backToHomepage": "Go back to Homepage", "bankUpdated": "Bank info successfully updated", "barnBox": "Barn-Box", "brand": "Brand", "bulkAction": "Bulk Action", "bulkDataUpdate": "Bulk Data Update", "cancel": "Cancel", "changePlan": "Change Plan", "changeTheme": "Change Theme", "choose": "<PERSON><PERSON>", "close": "Close", "closeOwner": "Are you sure you want to close this owner?", "comingSoon": "Coming soon", "comment": "Comment", "compareBalance": "Compare Balance", "confirm": "Confirm", "confirmChangePayment": "Are you sure you want to change Payment Status to 'Payment Plan' ?", "confirmCloseModal": "Are you sure you want to cancel? All data will be lost.", "confirmContinue": "Are you sure you wish to continue?", "confirmDelete": "Are you sure you want to delete?", "confirmMultipleHorseTreatment": "Would you like to change only this procedure or all recurring procedures in this series?", "confirmUpdate": "Do you want to update this task only or all the tasks associated with this original task request?", "confirmation": "Confirmation", "content": "Content", "continue": "Continue", "copied": "Link copied to clipboard.", "copy": "Copy", "copyLink": "Copy Link", "copyTo": "Copy to", "create": "Create", "credits": "Credits", "csv": "CSV", "currentHorse": "Current Horse", "dataErrorMessage": "Data error", "day": "Day", "days": "Days", "delete": "Delete", "deleteContent": "Are you sure you want to delete this {value}?", "deleteGeneral": "Delete General", "deleteProcedure": "Delete Procedure", "deleteSetting": "Are you sure you want to delete this setting?", "deleteSuccess": "{value} has been deleted successfully.", "deleteSuccessMsg": "Delete success!", "deleteTransport": "Delete Transport", "departureNotifications": "Departure Notifications", "description": "Description", "discard": "Discard", "discardCotent": "Are you sure you want to discard this {value}?", "discardSuccess": "{value} has been discard successfully", "download": "Download", "draft": "Draft", "edit": "Edit", "editGeneral": "Edit General", "editHorseOwnership": "Edit Horse Ownership", "editOwnership": "Edit Ownership", "editProcedure": "Edit Procedure", "editProfile": "Edit Profile", "editSuccess": "{value} successfully", "editSuccessPublish": "{value} successfully published", "editTitle": "Edit {value}", "editTransport": "Edit Transport", "email": "Email", "emailAttachment": "<PERSON><PERSON>", "end": "End", "error": "Unknown error occurred. Please try again <NAME_EMAIL>", "export": "Export", "exportCSV": "Export CSV", "exportOwners": "Export Owners", "exportPDF": "Export PDF", "exportToCsv": "Export to CSV", "exportToPdf": "Export To Pdf", "feedSchedule": "Feed Schedule", "filter": "Filter", "filterBarn": "<PERSON><PERSON>", "filterFeedTime": "Filter Feed Time", "filterLocation": "Filter Location", "finish": "Finish", "form": "Form", "forward": "Forward", "general": "General", "generalTask": "General Task", "group": "Group", "height": "Height", "hide": "<PERSON>de", "horse": "Horse", "horseAccess": "Horse Access", "horseIdentification": "Horse Identification", "horseInsurance": "Horse Insurance", "horseNote": "Horse Note", "horseSale": "Horse Sale", "horses": "Horses", "image": "Image", "import": "Import", "info": "Info", "inputAPIKey": "Input API Key", "inputComment": "Input Comment", "inputNote": "Input Note", "inputPassword": "Input Password", "inputUsername": "Input Username", "insert": "Insert", "invalidNumber": "Please enter a valid number", "inviteOwner": "Invite Owner", "invoiced": "Invoiced", "item": "<PERSON><PERSON>", "justThisTask": "Just this Task", "lastDentist": "Last Dentist", "lastFarrier": "Last Farrier", "lastService": "Last Service", "later": "Later", "links": "Links", "markAsCompleted": "<PERSON> Completed", "masterConfig": "Configure Master Data", "media": "Media", "minimumNumber": "Please enter the minimum number of 0", "mobile": "Mobile", "month": "Month", "name": "Name", "new": "New", "newDayRate": "New Day Rate", "newNote": "New Note", "next": "Next", "no": "No", "noData": "No data", "noIncompleteTask": "There are no incomplete tasks", "nothingFound": "Nothing found", "notice": "Notice", "notifications": "Notifications", "notificationsSettings": "Notifications Settings", "ok": "OK", "organizationConfig": "Organisation Config", "other": "Other", "owner": "Owner", "password": "Password", "pay": "Pay", "paymentNote": "Payment note", "paymentPlan": "Payment Plan", "paymentSettings": "Payment Settings", "pdf": "PDF", "preview": "Preview", "previous": "Previous", "print": "Print", "procedure": "Procedure", "profile": "Profile", "publish": "Publish", "rat": "Rat", "reactivate": "Reactivate", "reason": "Reason", "recurringTask": "Recurring task", "refresh": "Refresh", "refreshOwnership": "Refresh Ownership", "reject": "Reject", "reload": "Reload", "reminder": "Reminder", "remove": "Remove", "removeProcedure": "Remove from Procedure", "requestSupport": "Request Support", "requireField": "This field is required", "resend": "Resend", "reset": "Reset", "restore": "Rest<PERSON>", "rules": "Rules", "run": "Run", "runSuccess": "Run successfully", "save": "Save", "saveAsDraft": "Save As Draft", "saveAsDraftSuccess": "{value} successfully saved as a Draft", "schedule": "Schedule", "scheduledOn": "Scheduled on", "search": "Search", "selectAll": "Select All", "selectDateRange": "Please select a date range", "selectTrainer": "Select Trainer", "send": "Send", "sendDebt": "Send debt to Thoroughbred Recoveries", "sendEmail": "Send Email", "sendEmailPreview": "Send Email Preview", "sendRequest": "Send Request", "sent": "<PERSON><PERSON>", "sentDebt": "Send debt to Thoroughbred Recoveries", "sentSupportSuccess": "Your request has been sent successfully.", "setReminder": "<PERSON>minder", "show": "Show", "smsDontHavePhoneMsg": "Request sent to Thoroughbred Payment.<br></br><br></br>Below owners will not receive messages since they don't have phone numbers in their profile and/ or their phone numbers are incorrect format", "smsReminder": "SMS Reminder", "sorry": "Sorry", "sortBy": "Sort by", "start": "Start", "status": "Status", "subject": "Subject", "submit": "Submit", "successfullyUpdate": "Successfully updated", "summary": "Summary", "switchVersionMsg": "Are you sure you want to switch back to Version 1?", "tableNoData": "There are currently no data", "taskType": "Task Type", "termConditions": "Terms & Conditions", "thisVideoDoesNotExist": "This video does not exist.", "time": "Time", "to": "To", "toEmail": "To Email", "today": "Today", "totalAmount": "Total Amount:", "trainer": "Trainer", "trainerName": "Trainer Name", "transport": "Transport", "tryAgain": "Try again", "typeSomethingToSearchForOwner": "Type something to search for owner.", "unassigned": "Unassigned", "unknownError": "{code} - Unknown error occurred. Please try again <NAME_EMAIL>", "unlink": "Unlink", "update": "Update", "updateOwnerships": "Update Ownerships", "updateSuccess": "{value} successfully updated.", "upload": "Upload", "uploadFailed": "File uploaded failed, please try again.", "username": "Username", "usernameInfo": "Contact <EMAIL> to set up this field", "utilities": "Utilities", "vaccination": "Vaccination", "videoProcessed": "This video is being processed and can not be played right now.<br></br> Please try again later", "videoUrl": "Video URL", "view": "View", "warning": "Warning!", "watchVideo": "Watch Video", "web": "Web", "week": "Week", "welcomeBack": "Welcome back, {name}!", "whoops": "Whoops! Page not found.", "width": "<PERSON><PERSON><PERSON>", "work": "Work", "yes": "Yes", "yesterday": "Yesterday"}, "comms": {"buttons": {"addGroupUpdate": "Add Group Update", "addStableUpdate": "Add Stable Update", "addSyndicateUpdate": "Add Syndicate Update"}, "emailInputTooltip": "Each report will automatically be sent to the Owners of this Horse who are registered and opted in to receive updates. If you wish to email others this report, please input their email to the box below.", "groupUpdate": {"form": {"labels": {"content": "Content", "greeting": "Greeting", "groupUpdate": "Group Update", "scheduledPublishTime": "Scheduled Publish Time", "title": "Title", "updateFor": "Update for"}, "placeholders": {"groupUpdate": "Horses"}}, "preview": "Preview Group Update", "tabTitle": "Group Update"}, "stableUpdate": {"addMedia": "Add Media", "allGroup": "All Groups", "allHorses": "All Horses", "allInvoicedOwners": "All Invoiced Owners", "allOwners": "All Owners", "clickToViewOwnerList": "Click to view Owner List", "confirmDiscard": "Are you sure you want to discard this {value}?", "confirmPublic": "Are you sure you want to send stable update to selected owners?", "content": "Content", "discardMessage": "Are you sure you want to {value} this stable update?", "excludeOwner": "Exclude Owner", "excludeSyndicateOwners": "Exclude Syndicate Owners", "form": {"labels": {"content": "Content", "greeting": "Greeting", "scheduledPublishTime": "Scheduled Publish Time", "title": "Title", "updateFor": "Update for"}}, "greeting": "Greeting", "invoicedOwners": "Invoiced Owners", "media": "Media", "ownerByHorses": "Owners By Horses", "owners": "Owners", "ownersByHorse": "Owners By Horse", "placeholders": {"sender": "All Senders"}, "previewStableUpdate": "Preview Stable Update", "previewSyndicateUpdate": "Preview Syndicate Update", "recipients": "To: ", "scheduledPublishTime": "Scheduled Publish Time", "selectExcludeOwner": "Select Exclude Owner", "selectHorse": "Select Horses", "selectInvoiceOwner": "Select Invoiced Owners", "selectOwnerGroup": "Select Owner Groups", "selectOwnerGroups": "Select Owner Group", "selectOwners": "Select Owners", "sender": "From: ", "stableUpdate": "The Stable Update permission is switched off for", "syndicateUpdate": "The Syndicate Update permission is switched off for", "tabTitle": "Stable Update", "tabTitleSyndicator": "Syndicate Update", "template": "Template", "title": "Title", "to": "To", "viewOwnerList": "Click to view Owner List", "warningChangeTemplate": "You are changing to other template, all data will be clear. Are you sure you want to change template?"}}, "dashboard": {"filterNav": {"date": {"customDateRange": "Custom date range", "last30days": "Last 30 days", "last48h": "Last 48h", "last7days": "Last 7 days", "today": "Today"}, "shareBy": "Shared by"}, "headerNav": {"allTasks": "All Tasks", "crm": "CRM", "reminders": "Reminders"}, "infusionsoft": {"clientCRM": "Client CRM", "color": "Color", "contactName": "Contact Name", "dam": "Dam", "date": "Date", "email": "Email", "foaled": "Foaled", "horse": "Horse", "insertNextAction": "Insert Next Action", "insertNote": "Insert Note", "insertTitle": "Insert Title", "nextAction": "Next Action", "nextActionDate": "Next Action Date", "noOwnerListedForThisHorse": "'There are currently no Owners listed for this Horse'", "note": "Note", "opportunities": "Opportunities", "owner": "Owner", "phone": "Phone", "refresh": "Refresh", "remainingShares": "Remaining Shares", "selectStage": "Select Stage", "selectedStage": "Selected Stage", "sex": "Sex", "share": "Share", "size": "Size", "stage": "Stage", "syncCompleted": "Sync to Infusionsoft completed", "title": "Title", "unknownErrorOccurred": "Unknown error occurred."}, "reminder": {"confirmDeleteMsg": "Are you sure you want to delete this reminder?", "description": "Description", "outOfDateMessage": "This reminder is out of date!", "reminder": "Reminder", "reminderContent": "{taskType} task reminder", "reminderContentFor": "{taskType} task reminder for {name}", "reminderTime": "Reminder Time", "setReminder": "<PERSON>minder"}, "taskTable": {"addExpense": "Add Expense", "headerTable": {"assignedTo": "Assigned To", "comment": "Comment", "description": "Description", "from": "From", "horse": "Horse", "horseStatus": "Horse Status", "location": "Location", "owner": "Owner", "procedure": "Procedure", "status": "Status", "time": "Time", "to": "To"}, "noData": "Sorry, there are no results that match your search.", "tableName": {"crm": "CRM", "general": "General", "procedure": "Procedure", "transport": "Transport"}, "totalStatus": {"cancelled": "Cancelled", "complete": "Complete", "pending": "Pending", "total": "Total"}, "transport": {"originalTransportFrom": "Original Transport of Task on ", "returnTransportFrom": "Returning Transport from Task on "}}}, "finance": {"configure": {"chartOfAccounts": {"accountCode": "Account Code", "accountName": "Account Name", "accountType": "Account Type", "chartOfAccounts": "Chart of Accounts", "deleteConfirmMessage": "Are you sure you want to delete this account?", "description": "Description", "descriptionPlaceholder": "Input description of Account", "newAccount": "New Account", "noData": "There are currently no accounts", "taxRate": "Tax Rate", "type": "Type"}, "taxRates": {"action": "Action", "addAComponent": "Add a Component", "addNewTaxRate": "Add New Tax Rate", "component": "Component", "compoundRadioLabel": "Compound (apply to taxed subtotal)", "deleteConfirmMessage": "Are you sure you want to delete this Tax Rate entry?", "editTaxRate": "Edit Tax Rate", "effectiveTaxRate": "Effective Tax Rate", "inputDescriptionOfTaxRate": "Input description of Tax Rate", "lockToolTip": "This rate cannot be edited", "name": "Name", "noData": "There are currently no tax rates", "selectTaxType": "Select Tax Type", "taxComponents": "Tax Components", "taxRateDisplayName": "Tax Rate Display Name", "taxRates": "Tax Rates", "taxType": "Tax Type", "totalTaxRate": "Total Tax Rate", "type": "Type"}, "trackingCategories": {"activeTrackingCategory": "Active Tracking Category", "addNewTrackingCategory": "Add New Tracking Category", "addNewTrackingCategoryOption": "Add New Tracking Category Option", "archivedTrackingCategory": "Archived Tracking Category", "categoryName": "Category Name", "comfirmRestoreMsg": "Restore {optionName} and its Tracking Category {categoryName}", "deleteConfirmMessage": "Are you sure you want to delete this tracking category?", "deleteOptionConfirmMessage": "Are you sure you want to delete this category option?", "disableLocationTracking": "Disable Location Tracking", "enableLocationTracking": "Enable Location Tracking", "enableLocationTrackingSuccessMsg": "You are now using location list as a tracking category.", "moreThan2TrackingCategoryActiveMessage": "A maximum of four categories in total (including no more than 2 active categories) can exist at any one time and you cannot add a new tracking category until an existing one has been archived or deleted.\nCheck Tracking Category settings in Xero for more details.", "name": "Name", "noData": "There is currently no Tracking Category", "noDataArchive": "There is currently no Archived Tracking Category", "optionName": "Option Name", "restoreAllMsg": "Restore all archived tracking option from {categoryName}", "trackingCategories": "Tracking Categories", "trackingCategoryCantBlankMsg": "A tracking can't be blank, there must be at least one option existing", "trackingCategoryName": "Tracking Category Name", "trackingCategoryOptions": "Tracking Category Options"}}, "credit": {"all": "All", "amount": "Amount", "applied": "Applied", "applyGSTTo": "Apply GST to", "chargeTo": "Charge To", "confirmThatYouWishToContinue": "Please note that the total amount being charged to the Owner(s) is less than the Service Fee preset. Please confirm that you wish to continue?", "creditDescription": "Credit Description", "date": "Date", "description": "Description", "gstAmount": "GST Amount", "gstRegisteredOnly": "GST-registered only", "infoTooltip": "Credit refers to any payments received and recorded in Prism/Xero or any Credit applied to this owner.", "invoiceMessage": "Invoice Message", "noOwnershipShare": "There is no ownership share. Please choose another horse", "owner": "Owner", "ownerShare": "Owner Share", "referenceNumber": "Reference Number", "source": "Source", "totalMoney": "Total Money"}, "credits": {"amount": "Amount", "description": "Description", "noCredit": "There are currently no credits.", "type": "Type"}, "dashboard": {"addToAdditionalInvoice": "Add to Additional Invoice", "amountAre": "Amount Are", "apply": "Apply", "buttons": {"addInvoice": "Add Invoice", "addQuickInvoice": "Add Quick Invoices", "agree": "I agree to", "agree1": "I authorise Thoroughbred Recoveries to access my Prism file only with respect to the account relating to this debt for the sole purpose of recovering identified debts.", "agreeFee": "I agree to this fee.", "cancel": "Cancel", "desDebt1": "You are about to commence the debt recovery process against this party. If you proceed, Thoroughbred Recoveries will commence recovery of outstanding amounts.", "desDebt2": "Charges are 15% commission on recovered funds under $5,000 and 10% on recovered funds over $5,000.", "directBtn": "Direct Debit", "editQuickInvoice": "Edit Quick Invoices", "export": "Export", "exportCsv": "Export to CSV", "exportPdf": "Export to PDF", "exportPendingList": "Export Pending List", "fees": "Fees:", "importSetupFile": "Import Setup File", "print": "Print", "quickInvoices": "Quick Invoices", "refresh": "Refresh", "sendAndDown": "Send Letter(s) and Download", "sendDebt": "Send Debt to Thoroughbred Recoveries", "sendLetters": "Send Letter(s)", "sendOfDemand": "Download Letter(s) of Demand", "sendRemind": "Send Reminder", "sendRequest": "Send Request", "sentLetterDemand": "Send Letter(s) of Demand", "setupDirectDebit": "Setup Direct Debit", "singleInvoice": "Single Invoice", "smsReminder": "SMS Reminder", "submit": "Submit", "tcFee": "Thoroughbred Recoveries' T&Cs and Fees", "unresponsive": "Unresponsive"}, "changeHorseInvoiceSetConfirmMsg": "You have made the changes to this horse's invoice set. Please select the action you want to do with the updates.", "closeAndSyncToXero": "Close and Sync to <PERSON><PERSON>", "confirmGSTAppliedToOwnerMsg": "Based on the selected tax rate setups, GST will be applied to {text} owner's invoice <strong></strong>, even though they are an overseas customer. Are you sure you want to proceed with applying GST for this owner?", "confirmKeepInvoiceLineMsg": "Do you want to keep these invoice lines?", "createInvoiceSyncToXeroMsg": "You have created custom invoice(s) to {ownerName}.<br></br>You can sync this(these) invoice(s) to Xero or do it later.", "createSuccess": "Invoice successfully created", "date": "Date", "debtRecovery": {"action": "Action", "comingSoon": "Coming soon", "dateSent": "Date Sent ", "dayElapsed": "Days Elapsed", "emailStatus": "Email Status", "errorDemand": "You have to select at least one owner to download Letter(s) of Demand", "errorLegal": "You have to select at least one owner to send to Legal", "errorLetterDemand": "You have to select at least one overdue amount for owner to send Letter(s) of Demand", "errorLink": "Unknown error occurred. Please try again <NAME_EMAIL>", "noData": "No Debt Recovery found", "note": "Note", "ownerAccount": "Owner Account", "paymentStatus": "Payment Status", "pendingAmount": "Pending Amount", "placeholderNote": "Insert Note", "remainingAmount": "Remaining Amount", "subNoData": "There is current no Debt Recovery", "successLegal": "Your requests are being processed and will be completed in a few minutes", "successNote": "The note successfully saved.", "tooltipUnresponsive": "Click on this button to filter owners who have not responded within 14 days since Letter of Demand sent"}, "debtorsReport": {"action": "Action", "afterThreeMonthAmount": "Over 90 Days", "balance": "Balance", "comingSoon": "Coming Soon", "credit": "Credit", "currentAmount": "Current", "haveDebtRecovery": "You have sent the Letter of Demand to owner", "noData": "There are currently no Debtors report.", "oneMonthAmount": "1-30 Days", "ownerAccount": "Owner Account", "reminder": "Reminder", "threeMonthAmount": "61-90 Days", "totalDue": "Total Due", "twoMonthAmount": "31-60 Days", "view": "View"}, "directDebit": {"action": "Action", "comingSoon": "Coming Soon", "confirmMessage": "You are about to debit from the selected owner(s). Do you want to proceed?", "confirmation": "Confirmation", "errorSelect": "Please select at least an item.", "exportAllCSV": "Export All to CSV", "importError": "Direct debit import failed. Please download the error file for details.", "lastDate": "Last Date", "lastDebit": "Last Debit", "lastDebitStatus": "Last Debit Status", "noData": "There is currently not any owners set up with Direct Debit.", "optOut": "This owner has opted out of Direct Debit", "ownerAccount": "Owner Account", "pendingAmount": "Pending Amount", "searchOwner": "Search Owner", "searchStatus": "Search status", "status": "Status", "successMessage": "The direct debit request has been submitted for processing"}, "doYouWishAddNewOwner": "Do you wish to add the new owner(s) to the ownership of the horse, now?", "enablePayment": "Enable Payment", "enablePaymentTooltip": "Switching this ON will display 'Pay Here' button in Invoice PDF; OFF will hide this button in Invoice PDF", "invoiceDueDate": "Invoice Due Date", "invoiceLines": "Invoice Lines", "invoiceMessage": "Invoice Message", "later": "Later", "noExpense": "There are currently no expenses added", "noThanks": "No thanks, I will update later", "notIncludeInDirectDebit": "Not Include in Direct Debit", "notIncludeInDirectDebitTooltip": "If this setting is enabled, the amount will be excluded from Direct Debit.", "notificationSendStatement": "You have to select at least one statement to send email", "off": "OFF", "on": "ON", "overview": {"filterBy": "Filter by", "ninetyDays": "Over 90 Days", "oneDays": "1-30 Days", "outstanding": "Outstanding", "overdue": "Overdue", "paid": "Paid", "sixtyDays": "61-90 Days", "statements": "Statement", "thirtyDays": "31-60 Days", "title": "Overview"}, "overwriteCurrentSet": "Overwrite current set", "owner": "Owner", "ownerToBeInvoiced": "Owner to be Invoiced", "payment": "Payment", "receivableAcc": "Receivable Acc", "receivableAccount": "Receivable Account", "sale": "Sale", "saleAmount": "Sale Amount", "selectHorse": "Select Horse", "selectOptionOfTrackingCategory": "Select Tracking Category", "selectOwner": "Select Owner", "sendEmail": "Send Email", "sentEmailReminder": "Send Overdue Reminder Email", "setExpensePresetConfirmMsg": "Do you want to set this config as Expense Preset for this horse?", "share": "Share (%)", "tabs": {"debtRecovery": "Debt Recovery", "debtorsReport": "Debtors Report", "directDebit": "Direct Debit"}, "taxRate": "Tax Rate", "these": "these", "this": "this", "total": "Total", "totalSaleToBeInvoiced": "Total Sale to be Invoiced:", "trackingCategory": "Tracking Category", "useThisOneOff": "Use this one-off"}, "finance": "Finance", "importInvoice": {"amount": "Amount", "confirmDelete": "Are you sure you want to delete this invoice? You will not be able to import it.", "dueDate": "Due Date", "excludeCost": "Exclude Cost", "invoicedBy": "Invoiced by", "referenceNumber": "Reference Number", "statementId": "Statement ID", "statementPeriod": "Statement Period", "totalInvoiceAmount": "Total Invoice Amount"}, "managementFeeExpense": {"chargeTo": "Charge To", "date": "Select Date", "examplePerShare": "Example Per Share", "extra": "Extra", "extraFee": "Extra Fee", "horseName": "Horse Name", "inputPercent": "Input Percent", "noHorse": "There is no Horse. Please choose another Owner", "noOwner": "There is no Owner. Please choose another horse", "ownerName": "Select Owner Account", "pershare": "Per share", "rate": "Rate", "selectDate": "Select Date", "selectHorse": "Select Horse", "selectOwnerAccount": "Select Owner Account"}, "modals": {"adjustDiscount": {"buttons": {"cancel": "Cancel", "refresh": "Refresh", "regenerateStatement": "Re-generate Statement"}, "statement": "Statement"}, "adjustPayments": {"buttons": {"cancel": "Cancel", "refresh": "Refresh", "regenerateStatement": "Re-generate Statement"}}, "modifyStatements": {"buttons": {"cancel": "Cancel", "next": "Next"}, "confirmations": {"messages": {"regenerateForAllOwnersOfHorse": "The system will re-generate statements of all owners of this horse. Are you sure you want to update?"}, "titles": {"warning": "Warning"}}, "labels": {"addNewInvoice": "Add new invoice", "adjustDiscount": "Adjust Discount", "adjustPaymentCashRefund": "Adjust Payment/Cash Refund", "selectTaxInvoiceToUpdate": "Please select the tax invoices which you want to update", "taxInvoice": "Tax Invoice"}, "regenerateStatement": "Re-generate statement"}, "regenerateStatement": {"alert": "Please check and approve the expenses in the list before proceeding to the next step. To approve, check all expenses to be approved on the left column.", "buttons": {"addCredit": "Add Credit", "addExpense": "Add Expense", "cancel": "Cancel", "dailyChargeExpense": "Daily Charge Expense", "refresh": "Refresh", "regenerateStatement": "Re-generate Statement"}, "confirmNotApprovedExpense": "You have <strong>{count}</strong> of <strong>{total}</strong> expenses worth <strong><amount></amount></strong> that are not approved. Click OK to continue", "modals": {"editExpense": "Edit Expense", "splitItem": "Split Line Item Cost"}, "statement": "Statement", "table": {"cell": {"total": "Total {amount}"}, "header": {"chargeTo": "Charge To", "cost": "Cost", "description": "Description", "expenseDate": "Date", "payAccount": "Account (Pay)", "recAccount": "Account (Rec)", "sale": "Sale", "supplier": "Supplier", "taxRate": "Tax Rate"}}, "tooltips": {"delete": "Delete", "edit": "Edit", "split": "Split"}}, "sendDirectDebit": {"form": {"buttons": {"cancel": "Cancel", "save": "Save"}, "fields": {"labels": {"content": "Content", "foaIds": "Select Owners", "groupIds": "Select Owner Group", "horseIds": "Owners by Horse", "subject": "Subject", "to": "To"}, "placeholders": {"foaIds": "Select Owners", "groupIds": "Select Owner Group", "horseIds": "Select Horse", "subject": "Input subject"}}, "options": {"labels": {"allGroups": "All Groups", "allOwners": "All Owners"}}}, "notifications": {"messages": {"sendEmailSuccess": "Your emails are being processed and will be completed in a few minutes"}}}}, "nav": {"configure": "Configure", "credits": "Credits", "dashboard": "Dashboard", "importInvoice": "Imported Invoices", "onboarding": "Onboarding", "ownercost": "Owner Cost", "rules": "Rules", "statements": "Statements", "transaction": "Transaction"}, "noTax": "No Tax", "onboarding": {"130Days": "1-30 Days", "3160Days": "31-60 Days", "6190Days": "61-90 Days", "balance": "Balance", "confirmCrawl": "You have successfully crawled {numberInvoice} opening balances for {numberOwner} owners on {createdDate}. Would you like to proceed with the next section?", "confirmFinish": "Are you sure you want to finish initializing the opening balance data for your portal?", "confirmImport": "You have successfully imported {numberInvoice} opening balances for {numberOwner} owners on {createdDate}. Would you like to proceed with the next section?", "crawlFromXero": "Crawl From Xero", "credit": "Credit", "current": "Current", "cutOffDate": "Cut-Off Date", "excludeFromPrims": "Exclude from Prism", "finishOnboarding": "Finish Onboarding", "fromArdex": "From Ardex", "fromTemplate": "From Template", "importFile": "Import File", "importFromFile": "Import From File", "importModal": {"downloadFile": "Download Template File", "line1": "To import opening balance file to system please follow the steps below...", "line10": "The file you import must be a CSV (Comma Separated Values) file. The name of your file should end with .csv", "line2": "Step 1. Download our opening balance template file.", "line3": "Start by downloading our Opening Balance CSV (Comma Separated Values) template file. This file has the correct column headings Prism needs to import your expense data.", "line4": "Note: If your data is exported from Ardex system, you can skip this step and move to Step 3 to upload file", "line5": "Step 2. Copy your data into the template", "line6": "Export your data as a comma separated list. Using Excel or another spreadsheet editor, copy and paste your data from the exported file into the Prism template. Make sure the expense data you copy matches the column headings provided in the template.", "line7": "IMPORTANT: Do not change the column headings provided in the Prism template.", "line8": "Step 3. Import File", "line9": "Select the file to import"}, "mappingContact": "Mapping contact", "onboarding": "Onboarding", "over90Days": "Over 90 Days", "prismContact": "Prism Contact", "providedContact": "Provided Contact", "receivableAccount": "Receivable Account", "searchOwner": "Type to Search Owner", "submit": "Submit", "taxRate": "Tax Rate", "totalDue": "Total Due", "typeToSearchOwner": "Type to Search Owner"}, "rules": {"SelectTransactions": "Select transactions you want to update:", "account": "Account", "accountPay": "Account (Pay)", "accountRec": "Account (Rec)", "addDentistRule": "Add Dentist Rule", "addFarrierRule": "Add <PERSON> Rule", "addOtherRule": "Add Other Rule", "all": "All", "allItems": "All Items", "allSupplier": "All Suppliers", "amount": "Amount", "annualRate": "Annual Rate", "cappedAmount": "Capped Amount", "category": "Category", "chargeInAdvance": "Charge in Advance", "chargeTo": "Charged To", "confirmCreateManagementFeeTransactionsForToday": "Do you want to create management fee transactions for today?", "confirmDeleteManagementFee": "Are you sure you want to delete this Management Fee rule?", "confirmExpense": {"chargeTo": "Charged To", "date": "Date"}, "confirmExpenseChange": "Confirm Expense Changes", "confirmUpdateExpensesCreatedByThisRule": "Do you want to update expenses created by this rule?", "cost": "Cost", "dayRate": "Day Rate", "deleteImportRule": "Are you sure you want to delete? This import rule will be lost.", "deleteMessage": "Are you sure you want to delete this {value} charge?", "description": "Description", "editDayRate": "Edit Day Rate", "editDentistRule": "Edit Dentist Rule", "editFarrierRule": "<PERSON>", "editOtherRule": "Edit Other Rule", "editProcedureRule": "Edit Procedure Rule", "editRule": "Edit Transport Rule", "editTransportRule": "Edit Transport Rule", "excludeHorses": "Exclude Horses", "excludeOwners": "Exclude Owners", "extra": "Extra", "feeType": "Fee Type", "frequency": "Frequency", "horses": "Horses", "inputAmount": "Input Amount", "inputPercent": "Input Percent", "inputRate": "Input Rate", "invoiceMessage": "Invoice Message", "invoicedBy": "Invoiced by", "lastModified": "Last Modified", "location": "Location", "method": "Method", "modal": {"amountAre": "Amount Are", "item": "<PERSON><PERSON>", "payableAccount": "Payable Account", "receivableAccount": "Receivable Account", "selectItem": "Select Specific", "selectSupplier": "Select Supplier", "taxRate": "Tax Rate", "trackingCategory": "Tracking Category"}, "name": "Name", "newOtherRule": "New Other Rule", "newProcedureRule": "New Procedure Rule", "newSubCharge": "New Sub Charge", "newTransportRule": "New Transport Rule", "numberOfMonths": "Number of months", "percent": "Percent", "pershare": "<PERSON><PERSON><PERSON>", "purchasesDescription": "Purchases Description", "rate": "Rate", "ratePerHorse": "Rate Per Horse", "reference": "Reference", "refreshManagementFee": "Refresh Management Fee", "repeatOn": "Repeat On", "repeatSettings": "Repeat Settings", "sale": "Sale", "saleDescription": "Sales Description", "selectExtra": "Select Extra", "selectItem": "Select Item", "selectTrainer": "Select Trainer", "startDate": "Start Date", "status": "Status", "supplier": "Supplier", "tab": {"dayRates": "Day Rates", "dentist": "Dentist", "expenseRule": "Expense Rule", "farrier": "<PERSON><PERSON>", "importRule": "Import Rule", "managementFee": "Management Fee", "method": "Method", "other": "Other", "procedure": "Procedure", "specific": "Specific", "transport": "Transport"}, "taxRatePay": "Tax Rate (Pay)", "taxRateRec": "Tax Rate (Rec)", "tooltipChargeInAdvance": "Enter the number of months that you want to charge Management Fee in advance, correspondingly to future owners' shares", "trackingCategoryP": "Tracking Category (P)", "trackingCategoryR": "Tracking Category (R)"}, "statements": {"action": "Action", "addCredit": "Add Credit", "addDayRates": "Add Day Rates", "addExpense": "Add Expense", "addPayment": "Add Payment", "addSingleInvoice": "Add Single Invoice", "attachment": "Attachment", "chargeTo": "Charge to", "comingSoon": "Coming Soon", "confirmCustomInvoiceNoxero": "You have {actionType} a custom invoice to {ownerName}.", "confirmDeleteMsg": "Are you sure you want to remove this invoice? This action will also delete all associated expenses.", "confirmEditSingleInvoice": "The system will re-generate statements of all owners of {horseName}. Are you sure you want to update?", "confirmEmail": "You cannot go back or undo statements once they are sent to owners. Do you want to continue?", "confirmSendStatement": "Are you sure you want to send email to owner(s)?", "download": "Download", "dueDate": "Due Date", "editSingleInvoice": "Edit Single Invoice", "errorSelect": "You have to select at least one statement to print", "exportAlltoCsv": "Export All to CSV", "fromDate": "From Date", "horse": "Horse", "horseOwnershipNotFull": "Total owner shares are not equal to 100%. Please review and update the Ownership split.", "inputNote": "Input Note", "location": "Location", "miltipleStatementConfirmDeleteMsg": "When you remove the invoice for this owner, all associated invoices for other owners, along with the expenses, will also be deleted.<br></br><br></br>The corresponding invoices in Xero will be automatically voided.<br></br><br></br>Are you sure you want to continue?", "missingFieldMsg": "There are some items missing some fields that will prevent them from being invoiced. Please ensure all Expenses have an Accounts Payable account assigned, Tax Rate and $ Amount and all Sales have a Accounts Receivable account assigned, Tax Rate and $ Amount.", "noDailyCharges": "No Daily Charges", "noDailyChargesEmpty": "All horse location status changes for the period have been recorded.", "noDailyChargesWarning": "Horses listed below are missing a location/status or the daily charge rates for a location/status. Click the edit icon to adjust both of these.", "noData": "There are currently no statements.", "noLocationStatus": "No Location/Status", "noStatementSelectedError": "You have to select at least one statement to print", "note": "Note", "notificationDraftSendStatement": "You have to update Charge of Account and Tax Rate before sending to owner", "notificationMultiDraftSendStatement": "You have to update Charge of Account and Tax Rate for single invoices which were synced from InfusionSoft before sending to owner", "notificationSendStatement": "You have to select at least one statement to send email", "ownerFinanceAccount": "Owner Account", "paymentStatus": "Status", "percentDiscount": "% Discount", "placeholderNote": "Insert Note", "print": "Print", "refresh": "Refresh", "selectExpense": "Select Expense", "selectOwner": "Select Owner", "sendEmail": "Send Email", "sendInvoices": "Send Invoices Email", "sendStatement": "Send Statement Email", "sentAllEmail": "All Emails have been scheduled for sending", "statementCode": "Statement ID", "statementPeriod": "Statement Period", "status": "Status", "statusApproved": "Statement has been approved by a trainer (in case staff create statement) or has been generated by trainer directly.", "statusCreate": "Statement has been created but not yet sent", "statusMarked": "The statement was marked as in dispute and needing resolution", "statusOpened": "Statement has been opened or Pay Now button has been clicked by the Owner", "statusOwner": "Statements have been email to Owner", "statusPaid": "Owner paid for this statement and is currently being processed by Prism Pay", "statusPayment": "A partial payment has been made against this invoice", "statusReceived": "An error was received whe the Owner tried to pay - <NAME_EMAIL>", "statusTrainer": "The statement was deleted by the Trainer", "successNote": "The note of statement saved.", "taxInvoicename": "Tax Invoice Name", "toDate": "To Date", "totalAmount": "Total Amount", "totalCreditAmount": "Credit", "totalDiscount": "Discount", "totalInvoice": "Amount", "totalSale": "Total Sale", "updateLocationStatus": "Update Location/ Status", "viewStatement": "View Statement"}, "taxExclusive": "Tax Exclusive", "taxInclusive": "Tax Inclusive", "transaction": {"$discount": "$ Discount", "%discount": "% Discount", "ExcludeBlankStatement": "Exclude blank statement", "account": "Account", "accountPay": "Account (Pay)", "accountPlaceholder": "Account", "actionRec": "Account (Rec)", "addCredit": "Add Credit", "addExpense": "Add Expense", "addExpenseLabel": "Add Expense", "addExpenses": {"addExpense": "Add Expense", "allOwners": "All horse owners", "amountAre": "Amount are", "chargeTo": "Charge To", "chooseExistingPreset": "<PERSON>ose From Existing Preset", "confirm": "Do you want to create a task related to this expense?", "confirmChangeLocationStatusCompleteTask": "By marking this transport task as completed on this date, you are changing the location/status history of <horse></horse> which will affect future billings.<br></br><br></br> To confirm the change to <horse></horse>'s location and status to <location></location> on <date></date> click Yes.", "confirmEditExpense": "You are about to edit an individual expense for <chargeTo></chargeTo> that was created as part of a group add. Do you wish to only edit this expense for <horse></horse> or do you wish to edit other expenses in the group?", "confirmUpdateExpense": "You are about to update a transaction which will change the corresponding task. Are you sure you want to continue?", "cost": "Cost", "date": "Date", "description": "Description", "editAllExpenses": "Edit all expenses", "editExpense": "Edit Expense", "editGroupExpense": "Edit Group Expense", "editThisExpenseOnly": "Edit this expense only", "extra": "Extra", "from": "From", "fromLocation": "From Location", "inputReferenceNumber": "Input Reference Number", "invoiceMessage": "Invoice Message", "item": "<PERSON><PERSON>", "markupLineItem": "Mark Up Line Item", "messageLocation": "The To and From location entries cannot be the same. Please select different locations.", "method": "Method", "payableAccount": "Payable Account", "placeholderChargeTo": "No Value Available", "purchaseDescription": "Purchase Description", "purchasesDescription": "Purchases description", "qty": "Qty", "receivableAccount": "Receivable Account", "referenceNumber": "Reference Number", "sale": "Sale", "salesDescription": "Sales description", "selectCategory": "Category", "selectHorse": "Select Horse", "selectItem": "Select Item", "selectOwner": "Select Owner", "selectOwners": "Select Owners", "selectSpecific": "Select Specific", "selectSupplier": "Select Supplier", "successMessage": "Expense successfully created", "successMessageGroupExpense": "Group Expense successfully created", "supplier": "Supplier", "taxRate": "Tax Rate", "to": "To", "toLocation": "To Location", "trackingCategory": "Tracking Category", "unit": "Unit", "updateGroupExpense": "Group Expense successfully updated", "warningChangeSupplier": "Are you sure you want to change Supplier? If you change Supplier, you can keep the expenses you have added or clear them all."}, "addGroupExpenseLabel": "Add Group Expense", "addInvoiceNote": "Add Invoice Note", "addManagementFee": "Add Management Fee", "addPayment": {"addMore": "Add More", "addPayment": "Add Payment", "addSuccess": "Payments have been added successfully", "description": "Description", "ownerAccount": "Owner Account", "paymentAmount": "Payment Amount", "paymentDate": "Payment Date", "reference": "Reference"}, "allSupplier": "All Suppliers", "almostThere": "Almost there!", "amount": "Amount", "amountAre": "Amount Are", "approveAll": "Approve All", "balance": "Balance", "chargeTo": "Charged To", "chargedTo": "Charged To", "comingSoon": "Coming Soon", "confirmClearTransaction": "If you change this value, all transactions you've just created will be cleared. Do you want to continue?", "confirmContinueSyncing": "Are you sure you want to continue without syncing to <PERSON><PERSON>?", "confirmDelete": "Are you sure you want to delete?", "confirmDeleteExpense": "Are you sure you want to delete this transaction?", "confirmDeleteNote": "Are you sure you want to delete this Note?", "confirmDeleteTransactionTask": "You are about to delete a transaction which will also remove the corresponding task. Are you sure you want to continue?", "confirmDisableXero": "If you turn off this configure, all existing purchase info will be clear. Are you sure you want to turn it off?", "confirmRunStatement": "This statement run may contain blank statement(s) with only opening balances. Do you want to generate them?", "confirmToremoveThisExpense": "Are you sure you want to remove this Expense?", "connectToXero": "Connect to Xero", "connecting": "Connecting...", "cost": "Cost", "costTotal": "Total Cost: ", "createInXero": "Switch this ON to add/edit Purchase Cost amount and Payables Account, Tax Rate and Tax Status. This will result in a Supplier Bill being created in Xero, for the Supplier you select.", "createInvoiceXero": "Create a Supplier Bill in Xero", "createStatement": "Create Statement", "createStatementWithDate": "Create Statement ({startDate} - {endDate})", "creating": "% creating...", "credit": "Credit", "credits": "Credits", "customExpense": "Custom Expense", "dailyChargeExpense": "Daily Charge Expense", "date": "Date", "deleteAllExpenses": "Delete all expenses", "deleteExpense": "Delete Expense", "deleteHorseNote": "Horse note has been deleted successfully", "deleteNote": "Delete Note", "deleteThisExpenseOnly": "Delete this expense only", "description": "Description", "disconnect": "Disconnect", "discount": "Discount", "edit": "Edit", "editCredit": "Edit Credit", "editDailyCharge": {"confirmEditDailyCharge": "Would you like to change horse location or day rate rule?", "contentDeleteDailyCharge": "Delete or edit location history record to change Daily Charge for the horse", "deleteDailyCharge": "Delete Daily Charge", "editDailyCharge": "Edit Daily Charge", "editDayRate": "Edit Day Rate", "editHorseLocation": "Edit Horse Location"}, "editExpense": "Edit Expense", "editExpenseLabel": "Edit Expense", "emailStatements": "Email Statements", "emptyOwnership": "There are currently no expenses.", "expenseConfirmation": "Expense Confirmation", "exportAll": "Export All", "exportAllToCSV": "Export All to CSV", "exportApproved": "Export Approved", "exportCsv": "Export to CSV", "exportIsProgress": "Export is in progress. If you want to export csv, it will stop the process. Are you sure you want to export the csv?", "exportUnapproved": "Export Unapproved", "extra": "Extra", "finishSyncToXero": "Finish and Sync to <PERSON><PERSON>", "fromDate": "From Date", "fromDateBeforeLive": "From must be greater than the live date", "fromDateThenEndDate": "The start date cannot be later than the end date.", "general": "General", "generateBlankStatement": "Generate blank statement", "generatingStatements": "Generating Statements", "generatingStatementsComplete": "Invoicing and Statement creation complete", "groupExpense": "Group Expense", "horse": "Horse", "horseOwnership": "Horses Ownership", "importExpense": "Import Expense", "importExpenseModal": {"accountError": "Account code <error></error> not found", "costError": "Cost/Sale amount's format is wrong. It must be the number", "dateError": "Date format is wrong. The date format must be DD/MM/YY", "downloadTemplate": "Download Template File", "exportCSV": "Export your expenses as a comma separated list. Using Excel or another spreadsheet editor, copy and paste your expense from the exported file into the Prism template. Make sure the expense data you copy matches the column headings provided in the template.", "exportTemplate": "Start by downloading our expenses CSV (Comma Separated Values) template file. This file has the correct column headings Prism needs to import your expense data.", "horseError": "<error></error> not found", "importCSV": "Select the file to import", "important": "IMPORTANT: Do not change the column headings provided in the Prism template. Dates are assumed to be in Australian format. For example, 25/12/2020.", "intro": "To import expenses to the system please follow the steps below...", "invoiceMessageError": "Some expenses with Invoice message is blank", "lineInvalid": "Invalid data detected at the line <error></error> in the imported file", "step1": "Step 1. Download our expense template file", "step2": "Step 2. Copy your expense into the template", "step3": "Step 3. Import the updated template file", "taxError": "Tax Rate <error></error> not found", "tryAgain": "Please update and try again", "warning": "The file you import must be a CSV (Comma Separated Values) file. The name of your file should end with .csv"}, "importInvalidFile": "File contains no expenses or all of them have been imported previously", "importNSWFail": "File contains no expenses or all of them have been imported previously", "importNSWInvoice": "Import NSW Invoice", "importNSWSuccess": "{number} expenses has been imported successfully", "include": "include", "included": "Included", "incompleteTasks": "Incomplete Tasks", "inputNote": "Input Note", "interest": "Interest", "interestAmount": "Interest Amount", "interestPeriod": "Interest Period", "interestRate": "Interest Rate", "invoiceMessage": "Invoice Message", "item": "<PERSON><PERSON>", "managementFee": "Management Fee", "messageFinish": "You have finished creating the Statements for the period.", "missingFields": "Missing Fields", "newCredit": "New Credit", "newExpense": "New Expense", "newNote": "New Note", "noDailyCharges": "No Daily Charges", "noData": "There are currently no expenses.", "noExpense": "There are currently no expenses.", "noReversal": "There is currently no reversal.", "noTask": "There are no pending {value} tasks", "notFound": "Nothing found", "note": "Note", "ownerAccount": "Owner Account", "previewStatement": "Preview statement", "procedure": "Procedure", "processing": "Processing", "qty": "Qty", "racingClearDay": "Racing within Clear Day Guidelines", "recvAccount": "Account (Rec)", "referenceNumber": "Reference Number", "reset": "Latest Billing Period", "reversal": "Reversal", "reversalType": "Reversal Type", "reversals": "Reversals", "reverseTotalAccount": "Reverse Total Account", "sale": "Sale", "saveHorseNote": "Horse note has been saved successfully", "selectDate": "Please select Statement Date :", "selectExpense": "Select Expense", "selectHorse": "Select Horse to review", "selectHorsePlaceholder": "Select Horse", "selectOwnerAccount": "Select Owner Account", "skipAll": "Skip All", "specificOwnerAccount": "Specific owner account", "split": "Split", "splitExpense": {"amountAud": "Amount AUD", "date": "Date", "description": "Description", "extra": "Extra", "horseName": "Horse Name", "item": "<PERSON><PERSON>", "owner": "Owner", "ownerShare": "Owner Share", "quantity": "Quantity"}, "statement": "Statement: ", "statementId": "Statement ID", "statementInfoStep1": "Tasks below are still pending. To invoice these items, you need to make sure they have been Completed. Click the Action button to complete and Invoice these tasks.", "statementInfoStep2": "Horse listed below do not have a full Ownership list (ie., 100%) and thus cant be invoiced. Click the Action button to edit the ownership if you want these horses to be Invoiced", "status": "Status", "stepsStatement": "Step {stepActive} of {steps} - {stepLabel}", "supplier": "Supplier", "synToXero": "Your last sync was at {date}  <br></br>Please sync to Xero to ensure all data is up-to-date before generating new statements.", "sync": "Sync", "synchroniseAll": "Synchronise all", "synchronising": "Synchronising...", "syncing": "Syncing", "syndicateFee": "Syndicate Fee", "taxIncluded": "Tax Included", "taxRate": "Tax Rate", "taxRatePay": "Tax Rate (Pay)", "taxRateRec": "Tax Rate (Rec)", "toDate": "To Date", "totalCost": "Total Cost", "totalCredit": "Total Credit: ", "totalDiscount": "Total Discount: ", "totalInterest": "Total Interest: ", "totalRemainingAmount": "Total Due", "totalSale": "Total Sale: ", "trackingPay": "Tracking (Pay)", "trackingRec": "Tracking (Rec)", "transport": "Transport", "undo": "Undo Statement", "undoStatement": "Are you sure you want to reverse all statements?", "undoStatementSuccess": "The statements have been undone", "updateFaymentsFromXero": "Update payments from Xero", "viewDetails": "View details", "viewNote": "View Note", "viewOwnerCost": "View Owner Cost", "waitProcessing": "Please wait for the processing...", "waitToDownload": "Please wait to download the file.", "warningCredit": "Review, add or update owner credits to be used in this statement run.", "warningDiscount": "Discounts are when you want to reduce the cost of the Invoices for one of your Owners. Please confirm any discounts or updates you wish to make.", "warningEmailStatement": "To email, click the Owners whom you want to email the statements to on the left and then click the purple Email button to send.", "warningExpenseConfirm": "Please check and approve the expenses in the list before proceeding to the next step. <br></br>To approve, check all expenses to be approved on the left column or the <strong>⋮</strong> to Approve/Skip All", "warningFinnishWithXero": "Now that you have finished creating the Statements for the period, you can choose to finish the process or Sync your expenses to Xero.", "warningHorseOwnership": "Horse listed below do not have a full Ownership list (ie., 100%) and thus cant be invoiced. Click the Action button to edit the ownership if you want these horses to be Invoiced", "warningMissingField": "Items below are missing some fields that will prevent them from being invoiced. Please ensure all Expenses have an Accounts Payable account assigned, Tax Rate and $ Amount and all Sales have an Accounts Receivable account assigned, Tax Rate and $ Amount", "warningPreview": "Please note that total amount does not include charity donation", "warningReverse": "Reversals are used to reverse the charges for the Owner listed, usually when the Owner is an Internal person, Reversing the charge will remove it from their account. <br></br>To reverse an invoice, check the Reversal column on the left, select the amount or horse to be reversed and the account to reverse to.", "warningSelectInvoice": "Please select at least one expense to import", "wasSelectedMesg": "This was selected to be invoiced. If you don't want to process this item anymore, please remove it in Add Single Invoice dialog.", "xeroError": {"forAssistance": "for assistance.", "goBack": "Go Back", "pleaseLoginWithTheMatchedXero": "Your Prism account is currently syncing with a different Xero account. Please login with the matched Xero account or email"}}}, "horse": {"archive": {"archive": "Archive", "archiveHorse": "Archive Horse", "archiveThisHorse": "Select the date for which you want to archive this horse", "archivedDate": "Archived Date", "moveThisHorseToActiveHorse": "Are you sure you want to move this horse to active list?", "ownerArchiveHorse": "By archiving this horse, you will no longer be able to view the horse in the list, and no horse updates will be sent to you. Are you sure you wish to continue?", "shareHorse": "Share Horse", "unachivedHorse": "Unarchive Horse"}, "arrivalAdvice": "Arrival Advice", "balance": {"credit": "Credit", "current": "Current", "miscellaneous": "Miscellaneous", "name": "Name", "noData": "There are currently no Horse balance.", "overdue": "Overdue", "refreshPayment": "Refresh payment", "shareholding": "Shareholding", "totalDue": "Total Due"}, "confirmAdvise": "Do you want to advise horse's owner(s) about the movement?", "create": {"add": "Add", "addAdjustmentDate": "Please add adjustment date", "addBarnSuccess": "<PERSON><PERSON> successfully created", "addBoxSuccess": "Box successfully created", "addHorse": "Add Horse", "addLocationSuccess": "Location successfully created", "addNewBarnSuccess": "Add new barn success", "addNewBoxSuccess": "Add new box success", "addNewCategorySuccess": "Add new category success", "addNewLocationSuccess": "Add new location success", "addNewStatusSuccess": "Add new status success", "addStatusSuccess": "Horse Status successfully created", "adjustmentDate": "Adjustment Date", "attachedDate": "Attached Date", "attachedTo": "Attached to", "barn": "<PERSON>n", "basicInfo": "Basic Info", "bonusSheme": "Bonus Scheme", "box": "Box", "chooseExistingPreset": "Choose from existing Preset", "chooseLocationStatus": "Choose Location & Status Presets", "colour": "Colour", "comingSoon": "Coming soon", "createHorse": "Create Horse", "dam": "Dam", "dateOfDeath": "Date of Death", "dateOfSale": "Date of Sale", "editHorse": "Edit Horse", "errorMicrochipExtract": "An error has been occurred during extraction of data from Racing Australia", "errorMicrochipNote": "Were sorry for the incovenience, and please note that there's not any cost occured.", "feiNumber": "FEI Number", "foaled": "Foaled", "foaledDate": "Foaled Date", "geneticMother": "Genetic Mother", "hemisphere": "Hemisphere", "horseCategory": "Horse Category", "horseIdLocation": "Horse Id Location", "horseName": "Horse Name", "horsePurchaseAtSale": "Horse Purchase at Sale", "horseType": "Horse Type", "horseValue": "Horse Value", "inputDam": "Input Dam", "inputFeiNumber": "Input FEI Number", "inputGeneticMother": "Input Genetic Mother", "inputHorseBrand": "Input Horse Brand", "inputHorseIdLocation": "Input Horse ID Location", "inputHorseName": "Input Horse Name", "inputHorseValue": "Input Horse Value", "inputInsuranceProvider": "Input Insurance Provider", "inputLifeNumber": "Input Life Number", "inputMarkings": "Input Markings", "inputMicrochip": "Input Microchip", "inputNationalNumber": "Input National Number", "inputNickName": "Input NickName", "inputNoteAboutInsurance": "Input Note About Insurance", "inputNsBrand": "Input Horse Brand", "inputOsBrand": "Input OS Brand", "inputPrice": "Input Price", "inputRacingColour": "Input Racing Colour", "inputReasonOfDeath": "Input Reason of Death", "inputRecipientMare": "Input Recipient Mare", "inputSalePrice": "Input Sale Price", "inputSilksLocation": "Input Silks Location", "inputSire": "Input Sire", "inputStallionBio": "Input Stallion Bio", "inputTrainerName": "Input Trainer Name", "insuredValue": "Insured Value", "lifeNumber": "Life Number", "liveFoalGuarantee": "Live Foal Guarantee", "location": "Location", "locationStatusPreset": "Location - Status Presets", "markings": "Markings", "microChip": "Microchip", "nameOfSale": "Name of Sale", "nationalNumber": "National Number", "newHorse": "New Horse", "nickName": "<PERSON>", "nothingFound": "Nothing found...", "nsBrand": "NS Brand", "origin": "Origin", "osBrand": "OS Brand", "purchasePrice": "Purchase Price", "racingColours": "Racing Colours", "reasonOfDeath": "Reason of Death", "recipientMare": "Recipient Mare", "salePrice": "Sale Price", "searchBonusSheme": "Bonus Scheme", "selectAll": "Select All", "selectBarn": "Select Barn", "selectBox": "Select Box", "selectHorse": "Select Horse", "selectHorseCategory": "Select Horse Category", "selectHorseType": "Select Horse Type", "selectOrigin": "Select Origin", "selectedHorse": "Selected Horse", "sex": "Sex", "silksLocation": "Silks Location", "sire": "<PERSON>e", "stallionBio": "Stallion Bio", "stallionBioMaximumCharacters": "You have entered more than the maximum 255 characters", "status": "Status", "studBookDataExtract": "Stud Book Data Extract", "trainerName": "Trainer Name", "yearToStub": "Year To Stub"}, "currentlyNoHorse": "There is currently no horse", "dashboard": {"baseline": "Baseline", "baselineFrom": "Baseline from", "maximunDayOfFeedleft": "The range between the start and end dates is greater than the allowed range. Maximum number of days: 44 days", "maximunDayOfTemp": "The range between the start and end dates is greater than the allowed range. Maximum number of days: 60 days", "maximunDayOfWeight": "The range between the start and end dates is greater than the allowed range. Maximum number of days: 180 days", "maximunDayOfXray": "The range between the start and end dates is greater than the allowed range. Maximum number of days: 360 days", "summary": {"bloodProfile": "Blood Profile", "feedLeft": "Feed Left", "general": "General", "generalNote": "General Note", "horseNumber": "Horse Number", "procedure": "Procedure", "raceNumber": "Race Number", "recurringTask": "Recurring task", "scope": "<PERSON><PERSON>", "temp": "Temp", "today": "Today", "tomorrow": "Tomorrow", "trotUp": "Trot Up", "vetNote": "Vet Note", "viewMore": "View more", "weight": "Weight", "work": "Work", "xray": "X-ray", "yesterday": "Yesterday"}}, "departureAdvice": "Departure Advice", "filter": {"add": "Add", "allHorses": "All Horses", "allStatuses": "All Statuses", "archived": "Archived", "blackBook": "Blackbook", "current": "Current", "lessFilters": "Less Filters", "moreFilters": "More Filters", "noStatusesFound": "No statuses found", "pending": "Pending", "searchHorse": "Search Horse", "searchSupplier": "Search Supplier", "selectHorses": "Select Horses", "selectLocation": "Select Location", "selectStatus": "Select Status", "selectTrainerPlaceholder": "Select Trainer"}, "general": {"category": "Category", "categoryTask": {"dentist": "Dentist", "farrier": "<PERSON><PERSON>", "others": "Others"}, "common": {"action": "Action", "assignedTo": "Assigned To", "category": "Category", "comment": "Comment", "date": "Date", "description": "Description", "empty": "There is currently no Task scheduled for this Horse", "status": "Status"}, "cost": "Cost", "createTaskSchedule": "Create Task Schedule", "dueDate": "Due Date", "dueTime": "Due Time", "editTaskSchedule": "Edit Task Schedule", "inputComment": "Input Comment", "label": {"chooseFromExistingPreset": "Choose from existing Preset", "saveAsPreset": "Save As Preset"}, "modal": {"confirmDeleteTask": "Are you sure you want to delete this task?", "confirmDeleteTaskComplete": "You are about to delete a task that may impact your transactions and owner billings. Are you sure you want to continue?"}, "newTask": "New Task", "notification": {"deleteHorseTaskError": "General task error", "deleteHorseTaskSuccess": "General task successfully deleted"}, "placeholder": {"selectCategory": "Select Category", "selectSpecific": "Select specific"}, "sale": "Sale", "specific": "Specific", "status": {"cancelled": "Cancelled", "completed": "Completed", "pending": "Pending"}, "statusLabel": "New Task", "taskSchedule": "Task Schedule", "toolTip": {"delete": "Delete", "edit": "Edit", "markAsCompleted": "<PERSON> Completed", "setReminder": "<PERSON>minder"}}, "history": {"action": "Action", "barn": "<PERSON>n", "barnBox": "Barn Box", "barnBoxHistory": "Barn Box History", "box": "Box", "duration": "Duration", "from": "From", "fromDateGreaterToDate": "The FROM date should not greater than the TO date", "loadMore": "Load more...", "location": "Location", "locationHistory": "Location History", "locationHistoryDeletedSuccess": "Location history has been successfully deleted.", "noDataBarnBoxHistory": "There is currently no Barn Box History for this Horse", "noDataLocationHistory": "There is currently no location history for this horse.<br></br>You can manually add or if you wish to import the movements history from other system, <NAME_EMAIL>.<br></br>This location history will automatically update as you move your horses using the Transport module.", "present": "Present", "somePendingProcedureTasks": "There are some Pending recurring Procedure tasks on this horse. Do you wish to delete them?", "to": "To"}, "horse": "Horse", "horseChart": {"noData": "Sorry, there are no results that match your search", "noDataDescription": "There are no records to display on this chart.<br></br> Once data is recorded, it will appear here for tracking and analysis.", "noDataTitle": "No Data found"}, "horseComment": {"placeholderComment": "Input Comment"}, "horseGear": {"addingOrRemovingGear": "Adding or removing gear in this screen does not update racing authority records. Official submission of gear changes is still required.", "pleaseNote:": "Please note:", "racingAustraliasAndNZRacing": "Racing Australias and NZ Racing are currently unable to provide a list of ALL gear your horses are approved to race in. Please add gear manually in this screen to reflect racing authority records initially.Thereafter, gear changes submitted to and approved by racing authorities will automatically update raceday gear.", "selectGear": "Select Gear"}, "horseOwnership": {"addAsIndividual": "Add as Individual", "addAsSyndicate": "Add as Syndicate", "addSyndicateOrIndividual": "This owner is already in the Prism database as a Syndicator and an Owner. Do you wish to add him as an Individual or as part of a Syndicate?", "adjustmentDate": "Adjustment Date", "approved": "Approved", "balance": "Balance", "clickToApprove": "Click to Approve", "clickToReject": "Click to Reject", "clickToRemove": "Click to Remove", "confirmDeleteHorseAccessRequest": "Are you sure you want to delete this owner's horse access request?", "confirmDeletePreviousHorse": "This Owner currently has shares in this horse. You need to transfer their existing share to another Owner or account before deleting this ownership. Do you wish to adjust the shareholding and delete this ownership?", "manuallySplitExpense": "The horse has below manually split expense(s):", "matchToExistingOwner": "Match to Existing Owner", "moveToOld": "Are you sure you wish to move them to Previous?", "newShare": "New Share (%)", "noData": "There are currently no Owners listed for this Horse", "noOwners": "There are no Owners.", "note": "Note", "noteRefreshOwnership": "*The system will add owners as new owners in the case 'Match to existing owner' is blank", "owner": "Owner", "ownerAlreadyExists": "This owner already exists in the list.", "ownershipInformation": "The ownership information for your horse is currently up to date in the Trainer Portal. Would you like to override the existing owner information for your horse?", "permissionHaveNowBeenUpdated": "Permissions have now been updated", "rejected": "Rejected", "resplit": "You must revisit and re-split them with the current owner shares", "reviewDetailsProvided": "If so, please review the details provided below, and match owners to an existing owner profile via the 'Match to Existing Owner' field or leave that field blank to add the owners.", "selectOwner": "Select Owner", "shareholding": "Shareholding", "thisHorseHasNowBeenAssignedToThisOwner": "This horse has now been assigned to this Owner", "tooltipAdjustmentDate": "Adjustment Date is the Date of Sale. Purchase and billing date for the new Owner will be PLUS ONE day from this date", "transactionDate": "Transaction Date", "transactionsForHorse": "There is a later transaction for this horse. Do you want to remove it and add the new one?", "updateHorseOwnershipOfMare": "This update may make the change to all expenses applied to the horse (including service fee transaction and other ongoing expenses). Please review its costs to make sure the portions of the amount split to owners are correct.", "warningRefreshOwnership": "Are you sure you want to update the latest ownerships from Trainer portal to your portal?", "warningRejectHorseAccessRequest": "Are you sure you want to reject this owner's horse access request?"}, "insurance": {"addAttachment": "Add Attachment", "adjustedPremium": "Adjusted Premium", "attachments": "Attachments", "claimsHistory": "Claims History", "confirmNotToSplitTheInsurancePremiumAmongTheOwners": "If you choose not to split the cost among owners, all results from owners, if any, will be discarded. Are you sure you want to proceed?", "confirmSubmitQuote": "Are you sure you want to submit quote request for below horse:", "coveragePercentage": "Coverage percentage", "declaration": "Declaration", "disclosures": "Disclosures", "doh": "DOH", "estPremium": "Est Premium", "estimatePremium": "Estimate Premium", "estimatePremiumNext": "Estimate Premium & Next", "expireDate": "Expire Date", "expiryDate": "Expiry Date", "fillBelowInformation": "Fill below information to get premium estimation from Furlong Insurance", "foalBloods": "<strong>Foal Bloods</strong>: Haemograms/full blood count is required for all foals 100% valued at $150,000+ or any foals with a qualified VC.", "foalVetCerts": "Foal Vet Certs", "furlongEquineInsurance": "Furlong Equine Insurance", "go": "Go", "horse": "Horse", "horseValue": "Horse Value", "horseWithoutOwner": "The premiums for the following horses have no owners to share the cost:", "howMuchCoveragePercentageWouldYouLikeForYourInsurance": "How much coverage percentage would you like for your insurance?", "ifYouDontCurrentlyHaveAFurlongEquineInsuranceAccount": "If you don't currently have a Furlong Equine Insurance account please call <strong>{name}</strong> on", "ignore": "Ignore", "inputExtra": "Input Extra", "instructionsForReviewingOwnership": "Below is the list of Owners and their Ownership % in the horse. You can manually adjust the % Insured and \"Save\" or have the Owners confirm themselves by selecting the owners and clicking \"Send Confirmation Request to Owners\"", "insurance": "Insurance", "insuranceProvider": "Insurance Provider", "insured": "Insured %", "itAppearsYouDoNotCurrentlyHaveAFurlongInsuranceNumber": "It appears you don't currently have a Furlong Insurance ID number.", "lSSOption": "LSS Option", "manuallyInput": "Manually Input", "next": "Next", "notSplitThePremiumToOwners": "Not split the premium to owners", "notesOnFoalVetCerts": "<strong>Foal Vet Certs</strong>: must be completed after 24hrs of age and include IgG. LSD of mare must be included on VC to confirm not premature. EVA Foal VC required. If foals coming on risk later than 24 hours vet cert must be date of inception, no earlier. Foal Bloods: Haemograms/full blood count is required for all foals 100% valued at $150,000+ or any foals with a qualified VC.", "okIGotIt": "Ok, I got it", "onTopExtra": "On-top extra", "onTopExtraTooltip": "If you'd like to add an extra amount on top of the premium split for the owners, please enter it here", "oops": "Oops!", "optIn": "Opt-in", "optOut": "Opt-out", "owner": "Owner", "ownerRegistration": "Owner Registration", "payable": "Payable", "pleaseCallToArrangeAnAccount": "Please call <strong>{name}</strong> on <phone></phone> to arrange an account", "pleaseSelectAtLeastOneOwnerToSendTheSurvey": "Please select at least one owner to send the survey", "policyNumber": "Policy Number", "previous": "Previous", "provisionalPremium": "Provisional Premium", "refreshOwnership": "Refresh Ownership", "refreshResult": "<PERSON><PERSON><PERSON>t", "reviewOwnership": {"title": "{horseName} - Ownership Split and % of Horse Insured", "title1": "Current Insurance Uptake - {horseName}"}, "saveNext": "Save & Next", "schedule": "Schedule", "selectInsuranceProvider": "If so, please select insurance provider or enter the details manually", "sendConfirmationRequestToOwners": "Send Confirmation Request to Owners", "sex": "Sex", "share": "Share", "snoozeFor30Day": "Snooze for 30 days", "splitPremium": {"amountAre": "Amount Are", "applyForAllHorsesCoveredUnderThisPolicy": "This action will apply for all horses covered under this policy.", "date": "Date", "description": "Description", "dueDate": "Due Date", "eExcludeDirectDebit": "Exclude Direct Debit", "extra": "Extra %", "generateExpensesOrInvoices": "Would you prefer to generate expenses or invoices for the premium?", "invoiceMessage": "Invoice Message", "receivableAccount": "Receivable Account", "taxRate": "Tax Rate", "trackingCategory": "Tracking Category"}, "startDate": "Start Date", "status": "Status", "step2Description": "The insured percentage is automatically calculated based on the Owner Registration. To make adjustments, please review the ownership percentage split for each owner using the 'Review' action. Please note that responses may take a few days to complete. Once you send out the survey to owners, you can safely close the form and return later to view the results.", "step2Note": "<strong>Note</strong>: The premium for each horse is currently To Be Confirmed (TBC) and not the final amount.", "stepsInsurance": "Step {stepActive} of {steps} - {stepLabel}", "sumInsured": "Sum Insured", "survey": {"confirmation": "Confirmation", "doYouWishToTakeInsurance": "Do you wish to take insurance?", "horseDetails": "Horse Details", "horseInsuranceConfirmation": "Horse Insurance Confirmation", "horseName": "Horse Name", "owner": "Owner", "premium": "Premium (TBC)", "reChoose": "If you wish to change your preference, please contact {stableName} directly.", "recorded": "Your preference has been recorded.", "responded": "You already submitted your preference at {hour} on {date} ", "stable": "Stable", "thanks": "Thank you for your submission.", "totalHorseValue": "Total Horse Value", "youChoose": "You chose {optIn} to take insurance for {share}% of {horseName}.", "youHaveChosen": "You have chosen {optIn} to take insurance for {share}% of {horseName}", "yourShare": "Your Share"}, "thePremiumWasNotSplitToAnyOwners": "The premium was not split to any owners", "toGetAnIndicativePremiumAndInsureTheHorse": "To get an indicative premium and insure the horse, please select <strong>{furlong}</strong> or to enter the details manually, please click <strong>{manualInput}</strong>.", "total": "Total", "unvote": "Unvote", "use": "Use", "vetCerts": "Foal Bloods", "wouldYouLikeToAddInsurance": "Would you like to add insurance for this horse?"}, "list": {"reportMediaNotice": "This horse has not had a Report or Media update{days, plural, =0 { for # day} =1 {for one day} =2147483647 {} other { for # days}}"}, "media": {"album": "Album", "applyThisDateToAll": "Apply this date to all photos in album", "audio": "Audio", "date": "Date", "description": "Description", "draft": "Draft", "editMedia": "Edit Media", "forward": {"modal": {"form": {"labels": {"allOwners": "All horse owners", "otherEmails": "Other emails"}, "placeholders": {"emails": "Email Address"}}, "title": "Forward Media"}}, "media": "Media", "mediaFiles": "{value} files", "mediaForwarded": "Media Forwarded", "newestToOldest": "Newest to Oldest", "photo": "Photo", "photos": "Photos", "publish": {"modal": {"title": "Publish Media"}}, "status": "Status", "title": "Title", "today": "Today", "upload": {"buttons": {"publish": "Publish", "saveAsDraft": "Save as Draft", "upload": "Upload"}, "dropzone": {"accepts": "Accepted formats: {formats}", "messages": {"audio": "Drag audio here or click to select files", "media": "Click here to upload a photo or paste a video link below.", "video": "Drag your video file here,<br></br>or click to select, or insert link below"}}, "errors": {"url": "Only YouTube or Vimeo link supported."}, "fields": {"album": "Album", "description": "Description", "horse": "Horse", "publishedDate": "Published Date", "title": "Title"}, "labels": {"existing": "Existing", "new": "New", "none": "None"}, "placeholders": {"description": "Description", "horse": "Horse", "publishedDate": "Published Date", "title": "Title", "titleSelect": "Select Existing Album", "url": "Insert Youtube or Vimeo link"}}, "videos": "Videos"}, "merge": {"duration": "Duration", "from": "From", "keep": "Keep", "location": "Location", "merge": "<PERSON><PERSON>", "mergeHorseHasCode": "This horse currently shares the same profile data as <strong>{name}</strong>. Do you wish to merge the historical data from <strong>{name}</strong> (ie., ownership{extraInfo}) to this new horse profile?", "mergeHorseNoCode": "You have a new horse registered, <strong>{name}</strong>, that matches this horse profile data. If they are the same horse, do you wish to merge all data (ie., ownership{extraInfo}) from this profile to <strong>{name}</strong>?", "noData": "No data", "noLocationHistory": "There is currently no location history for this horse", "noOwnership": "There are currently no Owners listed for this horse", "noOwnershipList": "There are currently no Owners listed for this Horse", "noPortal": "No Portal", "owner": "Owner", "ownerInformation": "Check this column to prevent the horse displaying in the Owner's portal", "reviewHorseLocationMessage": "Please review the Horse Location History data provided below for accuracy and completeness. If it is ready to merge horses, please click the <strong>Merge</strong> button.", "reviewHorseLocationTitle": "Review Horse Location History", "reviewHorseOwnership": "Please review the final Horse Ownerships data listed below to ensure its accuracy before proceeding.", "reviewHorseTitle": "Review Horse Ownership", "share": "Share", "to": "To"}, "nav": {"balance": "Balance", "breeding": "Breeding", "dashboard": "Dashboard", "expense": "Expense", "media": "Media", "note": "Note", "procedure": "Procedure", "profile": "Profile", "racing": "Racing", "report": "Report", "statistics": "Statistics", "work": "Work"}, "note": {"amTemp": "AM Temp", "attachment": "Attachment", "createHorse": "Create Horse", "createNote": "Create Note", "createdSuccessfully": "Created successfully", "date": "Date", "deleteNote": "Are you sure you want to delete this Note?", "deleteNoteButton": "Delete Note", "editNote": "Edit Note", "emailInfo": "If you wish to email other parties, then please input their email to the box below.", "emptyDataTitle": "No Horse Notes found", "enterRaceInfo": "Input Race Info", "enterRecord": "Input Record", "errorMessage": "There is already a Note of this {name} recorded on this date. Please select another date or edit the existing Note on this date.", "feedAM": "Feed AM", "feedPM": "Feed PM", "flag": "Flag", "horseName": "Horse Name", "measure": "Measure", "newNote": "New Note", "noNotes": "There is currently no Note for this Horse.", "note": "Note", "noteDetails": "Note Details", "plannedRace": "Planned Race", "plannedRaceDate": "Planned Race Date", "pmTemp": "PM Temp", "record": "Record", "recordNotFound": "This record has been deleted. Please select another record.", "requiredFeedLeft": "Please select Feed AM or Feed PM", "requiredTemp": "Please enter AM or PM", "selectFeedAM": "Select Feed AM", "selectFeedPM": "Select Feed PM", "selectHorse": "Select Horse", "selectRecord": "Select Record", "selectType": "Select Type", "sentEmail": "This note sent to emails successfully", "type": "Type", "updated": "Updated"}, "owners": {"adjustShareholding": "Adjust Shareholding", "balance": "Balance", "buy": "Buy", "completed": "Completed", "confirmMove": "Are you sure want to move this Owner from Others to Previous Owner?", "confirmMoveOwnerFromCurrentToPrevious": "This Owner currently has shares in this horse. You need to transfer their existing share to another Owner or account before moving this Owner to the Previous list. Do you wish to adjust the shareholding and move this Owner to the Previous list?", "contentEmail": "Please see the below attachment containing the ownership details for Horse: ", "current": "Current", "date": "Date", "description": "Description", "emailAddress": "Email Address", "emptyDataDescription": "There are currently no Others listed for this Horse", "emptyDataTitle": "No Horse Others found", "firstPurchased": "First Purchased", "gst": "GST", "lastPurchased": "Last Purchased", "lastShareholding": "Last Shareholding", "lastSold": "Last Sold", "managementFee": "Management Fee", "managingOwner": "Managing Owner", "managingOwnerSuccess": "Managing owner successfully set", "masterAccount": "Master Account", "moveToOthers": "Move to Others", "moveToPrevious": "Move to Previous", "name": "Name", "noCurrent": "There are currently no Owners listed for this Horse", "noHistoryTransaction": "There are currently no Transactions yet.", "noPrevious": "There are currently no previous Owners listed for this Horse", "noRegistered": "There are currently no registered owners for this horse", "other": "Others", "otherMember": {"addMember": "Add member", "confirmDelete": "Are you sure you wish to remove {owner} from this Owner?", "membersSuccessfullyUpdated": "Member successfully updated"}, "owners": "Owners", "pending": "Pending", "phone": "Phone", "previous": "Previous", "prismRegistered": "Prism Registered", "procedure": "Procedure", "registered": "Registered", "sell": "<PERSON>ll", "shareholding": "Shareholding", "status": "Status", "subjectEmail": "Owner list of ", "totalOwnershipShare": "Total Ownership Share: ", "totalShare": "Total Ownership Share", "transactionsHistory": "Transaction History", "unknownError": "An unknown error occurred", "vaccination": "Vaccination"}, "profile": {"barn": "<PERSON>n", "basicInfo": {"age": "Age", "allFeeds": "All Feeds", "allSupplements": "All Supplements", "assignedTo": "Assigned To", "assignedToPlacholder": "Select Jockey", "bonusSheme": "Bonus Scheme", "breedrPhoto": "Breedr Photo", "colour": "Colour", "comment": "Comment", "competitionTacks": "Competition Tack", "competitionTacksPlaceHolder": "Click on Edit icon to update competition tack for horse", "dam": "Dam", "feed": "Feed", "feedPlacholder": "Click Edit icon to update feed for horse", "feedSchedule": "Feed Schedule", "feiNumber": "FEI Number", "foaled": "Foaled", "geneticMother": "Genetic Mother", "horseCategory": "Horse Category", "horseCode": "Horse Code", "horseIdLocation": "Horse ID Location", "horseName": "Horse Name", "horseType": "Horse Type", "lifeNumber": "Life Number", "markings": "Markings", "microchip": "Microchip", "nationalNumber": "National Number", "nsBrand": "NS Brand", "origin": "Origin", "osBrand": "OS Brand", "pedigree": "Pedigree", "racedayGear": "Raceday Gear", "racedayGearPlacholder": "Click Edit icon to update gear for horse", "racingColour": "Racing Colour", "recipientMare": "Recipient Mare", "sectionTitle": "Basic Info", "selectFeeds": "Select Feeds", "selectHorse": "Select Horse", "selectHorsePlacholder": "Selected Horse", "selectSupplements": "Select Supplements", "sex": "Sex", "showAdvertisements": "Show Horse Advertisements in Horse Media Update", "silksLocation": "Silks Location", "sire": "<PERSON>e", "tack": "Tack", "tackPlaceHolder": "Click Edit icon to update tack for horse", "workComment": "Work Comment", "workGear": "Work Gear", "workGearPlacholder": "Click Edit icon to update gear for horse", "workRider": "Work Rider", "workRiderPlacholder": "Click Edit icon to update work rider for horse"}, "horse": "Horse", "location": "Location", "newNote": "New Note", "noProcedureData": "There is currently no vaccination history for this horse", "selectBarn": "Select Barn", "selectHorse": "Select Horse", "selectLocation": "Select Location", "syndicate": {"country": "Country", "location": "Location", "postCode": "Post Code", "sectionTitle": "Syndicate", "state": "State", "suburb": "Suburb"}, "trainer": {"abn": "ABN", "address": "Address", "company": "Company", "emailAddress": "Email Address", "fullName": "Full Name", "gstResgistered": "GST Registered", "mobileNumber": "Mobile Number", "no": "No", "sectionTitle": "Trainer", "yes": "Yes"}, "vaccinationHistory": "Vaccination History"}, "racing": {"plannedRaces": {"title": "Planned Races"}, "racingCalendar": {"TBC": "TBC", "addRacePlan": "Add race plan", "addRacePlanFailed": "Failed to add plan note for race!", "addRacePlanSuccess": "Add plan note for race successfully!", "age": "Age", "ages": {"2YO": "2YO", "3YO": "3YO", "4YO": "4YO", "5YOANDUP": "5YO and UP"}, "class": "Class", "distance": "Distance", "horse": "Horse", "location": "Location", "meeting": "Meeting", "noData": "We couldn’t find any horses matching your search.", "noHorses": "No horses to display. Try using the search or filters.", "noRaceScheduled": "No races scheduled", "race": "Race", "racePlanning": "Race Planning", "search": "Search", "searchForRaces": "Search for races", "searchHorse": "Search Horse", "searchRace": "Search race", "selectAge": "Select Age", "selectAll": "Select All", "selectHorse": "Select Horse", "selectLocation": "Select Location", "selectSex": "Select Sex", "selectStatus": "Select Status", "sex": "Sex", "state": "State", "status": "Status", "time": "Time", "today": "Today", "type": "Type"}, "racingHistory": {"action": "Action", "date": "Date", "distance": "Distance", "emptyDataDescription": "There is currently no Racing history for this horse.", "emptyDataHarnessDescription": "There is currently no Racing history for this horse.", "emptyDataTitle": "No Recent Race Results found", "horse": "Horse", "jockey": "<PERSON><PERSON>", "mrg": "Mrg", "pos": "Pos", "race": "Race", "sp": "SP", "title": "Racing History", "track": "Track", "trainer": "Trainer", "wgt": "Wgt"}, "upcomingRaces": {"emptyDataDescription": "There is currently no Racing scheduled for this horse.", "emptyDataHarnessDescription": "There is currently no Upcoming Races.", "emptyDataTitle": "No Upcoming Races found", "title": "Upcoming Races"}}, "report": {"400m": "400m", "800m": "800m", "addAttachment": "Add Attachment", "addLinks": "Add Links", "addReport": "Add Report", "allStatus": "All Status", "allTypes": "All Types", "areYouSureDeleteMedia": "Are you sure you want to delete this Media?", "bar": "Bar", "barrier": "Barrier", "confirmGroupDelete": "This report was created by a Group Update. If you want to delete the report, the Group Update will be deleted as well. Are you sure you want to delete it?", "confirmHorseReport": "You are about to send a report with nothing in it. Are you sure you want to continue?", "confirmOwnershipCount": "This horse currently doesn't have any owners. Do you really want to continue?", "content": "Content", "createdBy": "Created by", "default": "<PERSON><PERSON><PERSON>", "draft": "Draft", "driver": "Driver", "filter": "Filter", "footerGallery": "Footer Gallery", "from": "From", "gallery": "Gallery", "greeting": "Greeting", "headerGallery": "Header Gallery", "horse": "Horse", "horseNo": "Horse No.", "horseReport": "Horse Report", "inRunningPosition": "{position, selectordinal, one {#st} two {#nd} few {#rd} other {#th}}", "jockey": "<PERSON><PERSON>", "labelGreeting": "Insert Greeting", "labelHorse": "Select Horses", "labelTitle": "Insert Title", "last5": "Last 5", "links": "Links", "margin": "<PERSON><PERSON>", "mediaDeleteSuccessfully": "Media deleted successfully.", "messageAddDraftSuccessPostRace": "Post-Race Report successfully saved as a Draft", "messageAddDraftSuccessPreRace": "Pre-Race Report successfully saved as a Draft", "messageAddSuccessPostRace": "Post-Race Report successfully published", "messageAddSuccessPreRace": "Pre-Race Report successfully published", "messageEditSuccessPostRace": "Post-Race Report successfully updated", "messageEditSuccessPreRace": "Pre-Race Report successfully updated", "messageSuccessCreateHorseReport": "Horse Report successfully saved as a Draft", "mrg": "Mrg", "name": "Name", "no": "No", "noDataSelectRace": "Sorry we could not find any Horses that have Accepted for a Race. Note that Pre-Race reports are available once a horse has Accepted to a Race and Final Fields available.", "noPhotoGallery": "There is no photo in the gallery yet.", "notFoundDescription": "This horse is yet to have a Report updated or sent to Owners. Use Prism Pre-Race, Post-Race and Horse Update templates to keep Owners updated and informed of their Horses progress.", "notFoundDescriptionOwner": "This horse is yet to have a Report updated or sent to Owners.", "notFoundTitle": "No updates found.", "note": "Note", "placeholderLink": "Website address", "pos": "Pos", "position": "Position", "postRace": "Post Race", "postRaceReport": "Post Race Report", "preRace": "Pre Race", "preRaceReport": "Pre Race Report", "preview": "Preview", "published": "Published", "race": "Race", "raceField": "Race Field", "raceInfo": "Race Info", "raceNo": "Race No.", "report": "Report", "scheduledPublishTime": "Scheduled Publish Time", "selectRace": "Select Race", "sendEmailSuccess": "Report sent to emails successfully", "sp": "SP", "startingOdds": "Starting Odds", "status": "Status", "template": "Template", "time": "Time", "title": "Title", "titleLink": "Title", "tooltipForward": "This report was forwarded to your owners", "track": "Track", "trainer": "Trainer", "trainerComment": "Trainer Comment", "type": "Type", "upload": "Upload", "warningChangeTemplate": "You are changing to other template, all data will be clear. Are you sure you want to change template?", "weight": "Weight", "wgt": "Wgt"}, "requestHorse": "Request Horse", "requestHorseAccess": {"addHorse": "Add Horse", "addHorseToBlackbook": "Add Horse to Blackbook", "awaitingTrainer": "AWAITING TRAINER", "awaitingTrainerMsg1": "Your request for access to the horse(s) listed below has been sent to the trainer(s) for approval to view the media/report.", "awaitingTrainerMsg2": "The horses listed below have been added to your Blackbook", "awaitingTrainerMsg3": "Once confirmed by the trainer, you'll receive updates directly from them about your horses.", "enterHorseNames": "Input horse names", "horseAccessAwaitingApproval": "Horse Access Awaiting Approval", "horseAddToBlackbook": "Horse Added To Blackbook List", "horseAddToBlackbookMsg": "The horses listed below have been added to your Blackbook.", "horseCantBeFound": "Horse Can Not Be Found", "horseCantBeFoundMsg": "Sorry we can't find below horses on file. Please contact your trainer directly or <NAME_EMAIL>.", "horseName": "Horse Name", "inviteTrainer": "<PERSON><PERSON><PERSON>", "inviteTrainerContent": "<p>Hi <strong>{trainer<PERSON><PERSON><PERSON><PERSON><PERSON>}</strong>, </p> <p> I just wanted to let you know that I am now using the new Prism horse management platform to keep up to date on my horses progress.</p> <p>Prism provides Trainers with a full suite of tools to manage their Stables, Staff and Owners and as I have <strong>{horseName}</strong> with you, I thought you might be interested.</p> <p>You can see more info on the system at <website>www.prism.horse</website> or email <email><EMAIL></email> and they can set up your account.</p> <p><strong>Cheers!</strong></p>", "inviteTrainerSubject": "Invite to join Prism", "label": "Request Horse Access", "message": "Enter the name of any horse you believe are missing from your profile and we send through to the Trainer for approval", "requestHorse": "Request Horse", "resendFailureMsg1": "Your request to add horses to your account was sent to stable some days ago.", "resendFailureMsg2": "Your request is currently pending approval from stable(s). You can only resubmit this request if no response is made 7 days after you submitted your first request.", "searchFavoriteHorse": "Search for your favorite horse", "sentDate": "Sent Date", "stableName": "Stable Name", "toDay": "Today", "trainerHasNotRegisteredPrism": "Trainer Has Not Registered With Prism", "trainerName": "Trainer Name", "trainerNotCurrentlyRegisteredPrism": "Trainer Not Currently Registered With Prism", "undefinedTrainer": "UNDEFINED TRAINER", "unregisteredTrainer": "UNREGISTERED TRAINER", "unregisteredTrainerMsg1": "The horses listed below have been added to your Blackbook.", "unregisteredTrainerMsg2": "To access to below horse(s), their trainer(s) first needs to register with Prism and then approve your request. Alternatively, you can send an email to <NAME_EMAIL> with details of the horse request and we can take a look for you."}, "share": {"action": {"reShareHorseOwnership": "Re-share Horse Ownership", "share": "Share", "syncHorseLocationAndStatus": "Sync Horse Location & Status", "syncTasks": "Sync Tasks"}, "confirmReject": "Are you sure you want to reject this horse?", "horseAndLocationStatus": "Horse & Location Status", "horseOwnership": "Horses Ownership", "infoHorseShare": "<strong>{stableName}</strong> shared the horse <strong>{horseName}</strong> to your portal. Below is horse information.", "input": {"label": {"from": "From", "share": "Share", "shareHorseOwnership": "Share Horse Ownership", "supplier": "Supplier", "syncHorseLocation": "Sync Horse Location & Status to recipient", "synchroniseTasks": "Synchronise Tasks"}, "placeHolder": {"select": "Select", "selectDate": "Select Date", "selectTaskType": "Select Tasks Type"}}, "message": {"doYouWantAddHorseToList": "Do you want to add <strong>{horseName}</strong> to your horse list.", "doYouWantAddOwnership": "Do you want to add <strong>{horseStableName}</strong> is owner of <strong>{horseName}</strong>?", "warningReshare": "Are you sure you want to re-share?", "warningShareHorseMessage": "Are you sure you don't wish to share the ownership of this horse to the supplier?", "warningSyncLocation": "Are you sure you want to sync?", "warningUnlink": "Are you sure you want to unlink?"}, "ownersAndOwnership": "Owners & Ownership", "reason": "Reason", "recipientName": "Recipient Name", "rejectNote": "Please advise to {name} why the horse has been rejected", "shareHorse": "Share Horse", "shareHorseOwnership": "Share Horse Ownership", "sharedFrom": "Shared From", "status": "Status", "supplier": "Supplier", "syncHorseLocationAndStatus": "Sync Horse Location & Status", "synchroniseTasks": "Synchronise Tasks", "text": {"accpetShareOwnerShip": "<strong>{horseStableName}</strong> has also shared the owners and ownership of <strong>{horseName}</strong>. Would you like to accept owner information?", "shareOwnershipNotify": "If you accept owner information, please match owners to an existing owner profile via the 'Match to Existing Owner' field. Or leave that field blank to add the owners."}, "tooltip": {"shareHoreSyncTask": "Pending and completed tasks entered in your Prism account, of the type(s) selected, will be immediately visible in your supplier's Prism account. Pending and completed tasks entered in your supplier's Prism account, of the type(s) selected, will be immediately visible in your Prism account.", "shareHoresStatus": "This will permanently synchronise the Location and Status you set in your Prism account, to the supplier's Prism account. The supplier will not be able to update location and status for their own records and owner billing. This option should only be selected if your supplier does not need to manage a horse's location or status for their own billing activities.", "shareHorseOwnership": "Note that this will notify the supplier(s) of each owner in the horse and allow them to check whether the owner exists in their Prism account or not. If the owner does exist in their Prism account, the supplier can match to an existing owner. If the owner does not exist in their Prism account, they can accept the automatic entry of the owner into their account. If the horse ownership changes, you can reshare the horse via the actions below."}}, "statistics": {"allWeatherOnly": "ALL WEATHER ONLY", "antiClockwise": "ANTI-CLOCKWISE", "averagePrizeMoney": "AVERAGE PRIZE MONEY", "bestHandicapRating": "BEST HANDICAP RATING", "clockwise": "CLOCKWISE", "currentRating": "CURRENT RATING", "firm": "FIRM", "firstUp": "FIRST UP", "good": "GOOD", "group": "GROUP", "heavy": "HEAVY", "last5": "LAST 5", "listed": "LISTED", "night": "NIGHT", "note": "*Updated at next Nomination", "placePercentage": "PLACE PERCENTAGE", "secondUp": "SECOND UP", "setWeights": "SET WEIGHTS", "soft": "SOFT", "straightTracksOnly": "STRAIGHT TRACKS ONLY", "synth": "SYNTH", "thirdUp": "THIRD UP", "totalResults": "TOTAL RESULTS", "turfTracks": "TURF TRACKS", "weightForAge": "WEIGHT FOR AGE", "winPercentage": "WIN PERCENTAGE"}, "update": {"adviseHorse": "Do you want to advise horse's owner(s) about the movement?", "barn": "<PERSON>n", "box": "Box", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "editLocationHistory": "Edit Location History", "editLocationHistorySuccess": "Edit Location History Success", "from": "From", "location": "Location", "locationStatusPreset": "location-status presets", "newLocationHistory": "New Location History", "printHorseCard": "Print Horse Card", "save": "Save", "saveAsPreset": "Save as Preset", "selectDate": "Select Date", "status": "Status", "updateHorseProfile": "Update Horse Profile", "validLocation": "You should select a valid Location", "validLocationStatus": "You should select a valid Location-Status", "validStatus": "You should select a valid Status", "youWantToDeleteLocationHistory": "Are you sure you want to delete this location history?"}, "yesArrivalAdvice": "Yes, Advise Arrival", "yesArrivalDeparture": "Yes, Advise De<PERSON>ture"}, "horseAvailable": {"add": {"PDSApprovalLetter": "PDS Approval Letter", "addNewMedia": "Add New Media", "addPedigree": "Add Pedigree", "addRaceRecord": "Add Race Record", "afslarNumber": "AFSL/AR Number", "allBonusScheme": "All Bonus Scheme", "attachment": "Attachment", "bloodstockCategory": "Bloodstock Category", "bloodstockExchangeLink": "Bloodstock Exchange Link", "bonusScheme": "Bonus Scheme", "bonusSchemeTooltip": "To display a horses bonus eligibility, <NAME_EMAIL> to enable this feature", "breedingCertificate": "Breeding Certificate", "category": "Category", "chooseCategory": "Choose Category", "chooseListingType": "Choose Listing Type", "colour": "Colour", "conditions": "Conditions", "confirmAccountNotRegister": "Your account has not been registered with Bloodstock Exchange yet. Do you want to create an account and push your horse to Bloodstock Exchange?", "countryOfBirth": "Country of Birth", "currentCountry": "Current Country", "currentLocation": "Current Location", "currentState": "Current State", "currentStatus": "Current Status", "dam": "Dam", "description": "Description", "foalAtFoot": "Foal At Foot", "foalAtFootBy": "Foal at Foot By", "foalSireBy": "Foal <PERSON><PERSON> By", "foaled": "Foaled", "genderFilter": "Gender Filter", "headline": "Headline", "horseDisplayName": "Horse Display Name", "horseName": "Horse Name", "inFoal": "In Foal", "inFoalTo": "In Foal To", "inputAfslarNumber": "Input AFSL/AR Number", "inputCondition": "Input Condition", "inputCurrentStatus": "Input Status", "inputFoalAtFootBy": "Input Foal at Foot By", "inputFoalSireBy": "Input Foal Sire By", "inputHeadline": "Input Headline", "inputHorseDisplayName": "Input Horse Display Name", "inputInFoalTo": "Input In Foal To", "inputLocation": "Input Location", "inputMembershipPrice": "Input Membership Price", "inputPrice": "Input Price", "inputPublicPrice": "Input Public Price", "listingType": "Listing Type", "membershipPrice": "Membership Price", "noMediaAttach": "There is no attachment file uploaded. <br></br>Click \"Add New Media\" to upload the file.", "noPedigreeAttach": "There is no pedigree file uploaded. <br></br>\nClick \"Add Pedigree\" to upload the file.", "pdsExpiryDate": "PDS/Expiry Date", "pedigree": "Pedigree", "price": "Price", "publicPrice": "Public Price", "publishTo": "Publish To", "raceRecord": "Race Record", "scoped": "<PERSON><PERSON>", "selectBreedingCertificate": "Select Breeding Certificate", "selectCategory": "Select Category", "selectCountry": "Select Country", "selectFileToUpload": "Select the file to upload", "selectFoalAtFoot": "Select Foal At Foot", "selectFoalSireBy": "Select Foal Sire By", "selectGenderFilter": "Select Gender", "selectHorse": "Select Horse", "selectInFoal": "Select In Foal", "selectPdsExpiryDate": "Select PDS/Expiry Date", "selectScoped": "Select Scoped", "selectState": "Select State", "selectXRayed": "Select X-Rayed", "sex": "Sex", "sire": "<PERSON>e", "sold": "Sold", "trainerDisplayName": "Trainer Display Name", "xRayed": "<PERSON><PERSON><PERSON><PERSON>"}, "addHorseAvailable": "Add Available Horse", "attachment": "Attachment", "bloodstockExchangeLink": "Bloodstock Exchange Link", "confirmDelete": "Are you sure you want to delete this available horse?", "deleteSuccess": "Delete Horse Sale success", "description": "Description", "editHorseAvailable": "Edit Available Horse", "headline": "Headline", "info": "Info", "media": "Media", "nav": {"addACurrentHorse": "Add Current Horse", "addAvailableHorse": "Add Available Horse", "available": "Available", "editACurrentHorse": "Edit Current Horse", "orderAvailableHorse": "Order Available Horse", "sold": "Sold"}, "notFoundContent": "There is currently no Available Horses.", "orderSuccess": "Order successfully", "pdsApprovalLetter": "PDS Approval Letter", "pedigree": "Pedigree", "raceInfo": {"age": "Age", "bexLink": "BEx Link", "bonusScheme": "Bonus Scheme", "colour": "Colour", "dam": "Dam", "foaled": "Foaled", "horseDisplayName": "Horse Display Name", "ownerDate": "Owner Date", "ownerPrice": "Owner Price", "price": "Price", "publicDate": "Public Date", "sex": "Sex", "sire": "<PERSON>e", "trainerDisplayName": "Trainer Display Name"}, "raceRecord": "Race Record", "setAsCoverImage": "Set as cover image", "sold": "Sold", "titleOrder": "Order Horses Available", "unpublishHorse": "Unpublish horse from Bloodstock Exchange"}, "horseUpdate": {"emailTooltipInfo": "Each report will automatically be sent to the Owners of this Horse who are registered and opted in to receive updates. If you wish to email others this report, please input their email to the box below.", "notFoundDescription": "You have not published any updates yet. Use Group Update template to keep Owners updated and informed of News in your stable.", "notFoundTitle": "No updates found.", "sendEmailSuccess": "Group Update sent to emails successfully!"}, "insurance": {"active": "Active", "addHorse": "Add horse", "age": "Age", "areYouWantDeleteThisHorse": "Are you sure you want to delete this horse?", "attachment": "Attachment", "billOwners": "Bill <PERSON>ers", "confirmation": "Confirmation", "delete": "Delete", "editInsurance": "Edit Insurance", "endDate": "End Date", "expiryDate": "Expiry Date", "horseListing": "Horse Listing", "horseValue": "Horse Value", "horses": "Horses", "insuranceProvider": "Insurance Provider", "insureHorse": "Insure Horse", "insured": "Insured %", "insurer": "Insurer", "invoiceStatus": "Invoice Status", "lastFive": "Last 5", "lastInsured": "Last Insured", "lastPlaced": "Last Placed", "lastRaced": "Last Raced", "lssOption": "LSS Option", "makeClaim": "Make Claim", "messageSendPolicy": "We will inform <PERSON>' team that you would like to learn more about the insurance policy, and they will contact you.<br></br><br></br> Are you sure you want to continue?<br></br>If so, you can leave your message and proceed.", "noNotes": "No Notes", "note": "Note", "payable": "Payable", "policyListing": "Policy Listing", "policyNumber": "Policy Number", "premium": "Premium", "premiumWasSplit": "Premium was split", "prizeMoney": "Prize Money", "sendEmailForPolicy": "Send Email for Policy", "sendPolicySuccess": "Your request has been sent successfully.", "startDate": "Start Date", "status": "Status", "sumInsured": "Sum Insured", "thereAreNoFiles": "There are no files", "totalPayable": "Total Payable", "totalSumInsured": "Total Sum Insured", "uninsuredHorses": "Uninsured Horses", "viewAttachment": "View Attachment", "viewInsurance": "View Insurance", "viewNote": "View Note", "viewPolicyDocument": "View Policy Document", "yourMessage": "Your Message"}, "lifetimeView": {"exportCsv": "Export CSV", "fortnight": "Fortnight", "label": "Lifetime View", "month": "Month", "today": "Today", "week": "Week", "weekSchedule": "Week Schedule"}, "media": {"all": "All", "audio": "Audio", "audioGallery": "Audio Gallery", "documents": "Documents", "gallery": {"noAudio": "There are currently no Audio files available for this Horse", "noImages": "There are currently no Images available for this Horse", "noVideos": "There are currently no Videos available for this Horse"}, "imageGallery": "Image Gallery", "images": "Images", "photos": "Photos", "uploadMedia": "Upload Media", "videoGallery": "Video Gallery", "videos": "Videos"}, "nav": {"breeding": "Breeding", "cms": "CMS", "comms": "<PERSON><PERSON><PERSON>", "dashboard": "Dashboard", "finance": "Finance", "financeRule": "To enable Prism Finance and Billing,\n <NAME_EMAIL>", "horse": "Horse", "horseSale": "Horse Sale", "insurance": "Insurance", "owner": "Owner", "prismPlus": "Prism Plus", "racing": "Racing", "report": "Report", "schedule": "Schedule", "staff": "Staff", "supplier": "Supplier", "syndicator": "Syndicator", "trainer": "Trainer", "workplace": "Wrk<PERSON>"}, "notifications": {"editOwnershipMessage": "The ownership of ${horses} have been changed due to new purchases. Please review horse ownership and make adjustment if needed.", "noNewNoti": "There are no new notifications.", "notifications": "Notifications", "permissionChangeQuestion": "Your permission has been changed. Would you like to apply it now or continue your current session?", "syncCompleted": "Synchronisation Completed", "syncFailed": "Synchronisation Error", "yesterday": "Yesterday"}, "owner": {"account": "Account", "accountBasic": "Basic Info", "activeAccount": "Active Account", "add": {"abn": "ABN", "accountManager": "Account Manager", "addNewOne": "Do you wish to add the new one?", "address": "Address", "bankAccountName": "Bank Account Name", "bsbBankAccountName": "BSB & Bank Account Number", "clientRanking": "Client Ranking", "company": "Company", "confirmSetFinanceEmail": "Do you also want to set this as the Finance/Account email address for this owner?", "country": "Country", "customGroups": "Custom Groups", "dateOfBirth": "Date of Birth", "dayBillDate": "day(s) after the bill date", "dayOverdue": "day(s) overdue", "debtor": "Debtor #", "displayName": "Display Name", "dob": "DOB", "emailAddress": "Email Address", "emailAlready": "This email address is already registered in your Owner list to the following:", "emailInfo": "The below field will update both the owner's contact email and login email", "firstName": "First Name", "gstRegistration": "GST Registration", "inputAbn": "Input ABN", "inputAccountManager": "This will be updated from Infusionsoft", "inputAddress": "Input Address", "inputBankAccountName": "Input Bank Account Name", "inputBsbBankAccountName": "xxxxxx-xxxxxxxxxx", "inputCompany": "Input Company", "inputCustomGroups": "Coming soon", "inputDebtor": "Input Debtor", "inputDisplayName": "Input Display Name", "inputEmailAddress": "Input Email Address", "inputEmails": "Input email address and hit return", "inputFirstName": "Input First Name", "inputInterestSettings": "Input Interest Settings", "inputInvoiceDueDate": "Input Invoice Due", "inputLastName": "Input Last Name", "inputMobileNumber": "Input Mobile Number", "inputOverallDiscount": "Input Overall Discount", "inputOwnerUserID": "Input Owner User ID", "inputPostCode": "Input Postcode", "inputPrismId": "Input Prism ID", "inputSilkColor": "Input Silk Color", "inputSpouse": "Input Spouse", "inputSuburb": "Input Suburb", "interestSettings": "Interest Settings", "internal": "Internal", "invoiceDueDate": "Invoice Due Date", "lastName": "Last Name", "mobileNumber": "Mobile Number", "overallDiscount": "Overall Discount", "overseasCustomer": "Overseas Customer", "ownerUserID": "Owner User ID", "postCode": "Postcode", "prismId": "Prism ID", "receiveStableUpdate": "Receive Stable Update", "requestSupport": "Request Support", "sameAsOwnerProfile": "Same as owner profile", "selectClientRanking": "Select Client Ranking", "selectCountry": "Select Country", "selectInterestAccount": "Select Interest Account", "selectInternal": "Select Internal", "selectReceiveStableUpdate": "Select Receive Stable Update", "selectState": "Select State", "selectTaxRate": "Select Tax Rate", "silkColor": "Silk Color", "spouse": "Spouse", "state": "State", "suburb": "Suburb", "warningUpdate": "By updating the Overall Discount %, you will override the discounts applied to each individual horse. Do you wish to continue?"}, "addAccount": "Add Account", "addDiscount": "Add Discount", "addNewOwner": "Add New Owner", "addNote": "Add Note", "addOwner": "Add Owner", "addPayment": "Add Payment", "allOwners": "All Owners", "amount": "Amount", "assignedTo": "Assigned to", "attachments": "Attachments", "attachments:": "Attachments: ", "balance": "Balance", "basicInfo": {"abn": "ABN", "accountManager": "Account Manager", "accountName": "Account Name", "addHorse": "Add Horse", "address": "Address", "allOwners": "All Owners", "awaitingConfirm": "Awaiting Confirmation", "bankAccountName": "Bank Account Name", "bankAccountNumber": "BSB & Bank Account Number", "breederID": "Breeder ID", "cancelDirectDebit": "Are you sure you want to cancel direct debit?", "changeToSynd": "Change to Synd", "changesInHorseOwnership": "Changes in Horse Ownership", "charityDonation": "Charity Donation", "clientRanking": "Client Ranking", "comingSoon": "Coming soon", "company": "Company", "confirmDeleteCard": "Are you sure you want to delete MYOB Card ID?", "confirmed": "Confirmed", "createLoginInfo": "Create Login info", "customGroups": "Custom Groups", "dateOfBirth": "Date of Birth", "debtor": "Debtor #", "directDebit": "Direct Debit", "editDisplayName": "Edit Display Name", "editOwner": "Edit Owner", "emailAddress": "Email Address", "emptyNotes": "Click Edit to add note", "exportOwners": "Export Owners", "fullName": "Full Name", "getLink": "Get Link", "groupOwners": "Group Owners", "gstRegister": "GST Registration", "inputMmyOBCardId": "Input MYOB Card ID", "interestRate": "Interest Rate", "invoiceDueDate": "Invoice Due Date", "lastLoginTime": "Last login time", "linkToTrainer": "<PERSON> To Trainer", "login": "<PERSON><PERSON>", "mobileNumber": "Mobile Number", "myOBCardId": "MYOB Card ID", "nettOwnershipScore": "Nett Ownership Score", "notFoundTitle": "Owner not found!", "notificationsError": "Error updating", "optOut": "Opt-out", "overallDiscount": "Overall Discount", "overallDiscountTooltip": "Overall discount will provide a % discount on this Owners entire bill each month.", "overseas": "Overseas Customer GST", "ownerUserID": "Owner User ID", "personNotes": "Personal Note", "prismId": "Prism ID", "prismIdTooltip": "To allow this customer to import invoices into their Prism portal, enter the customer's Prism ID or click Send Request", "profile": "Profile", "reactivate": "Reactivate", "receiveStableUpdates": "Receive Stable Updates", "receiveSyndicateUpdates": "Receive Syndicate Updates", "registered": "Registered", "remove": "Remove", "resendInvitation": "Resend Invitation", "resendRequest": "Resend Request", "resetPassword": "Reset Password", "sameAsOwnerProfile": "Same as owner profile", "sectionTitle": "Basic Info", "sendPrismId": "Are you sure you want to send Prism ID Request to owner?", "sendPrismIdSuccess": "Your emails are being processed and will be completed in a few minutes", "sendRequest": "Send Request", "silkColor": "Silk Color", "spouse": "Spouse", "suspend": "Suspend", "syndicate": "Syndicate", "syndicationManagementFee": "Syndication Management Fee", "tooltipCheckbox": "Turn this off if the owner does not wish to receive your newsletters or general updates. This will not switch off Horse Updates"}, "cancel": "Cancel", "changeHorseOwnershipReport": "Changes in Horse Ownership Report", "changesInHorseOwnership": "Changes in Horse Ownership", "chargedTo": "Charged To", "comment": "Comment", "communications": "Communications", "confirmDeleteNote": "Are you sure you want to delete this Note?", "confirmRemainingExpenses": "You cannot remove this owner because of their pending expenses. To proceed with the removal, please go to the Transactions list, locate the pending expenses, and remove them first.", "confirmRemove": "Are you sure you want to remove {name}.", "createNew": "Create New", "crm": "CRM Task", "currentlyNoOwner": "There is currently no owner", "date": "Date", "dayRateDiscount": "Day Rate Discount", "deleteCreditWarningMsg": "Are you sure you want to remove this payment?", "deleteNote": "Delete Note", "deleteOwnerSuccess": "The owner has been successfully deleted.", "description": "Description", "discount": "Discount", "edit": "Edit", "editAccount": "Edit Account", "editNewOwner": "Edit Owner", "editNote": "Edit Note", "email": "Email", "filter": {"allOwners": "All owners", "approved": "Approved", "awaitingApproval": "Awaiting approval", "neverLoggedIn": "Never logged in", "suspended": "Suspended"}, "forceRemove": "Force Remove", "from": "From", "groupOwners": "Group Owners", "history": "History", "horse": "Horse", "horseList": {"addHorse": "Add Horse", "addHorseToAccount": "Add Horse To Account", "confirmDeleteDiscount": "Are you sure you want to delete discount?", "currentShare": "Current Share", "horse": "Horse", "managementFee": "Management Fee", "noHorse": "There is currently no horse.", "noHorseAccount": " <PERSON><PERSON> Add Horse to start the horse list for this account", "overallDiscount": "Overall Discount", "selectHorse": "Select Horse", "selectRule": "Select Rule"}, "horses": "Horses", "inputNote": "Input Note", "inputSilkColor": "Input Silk Color", "inputTitle": "Input Title", "invoiceMessage": "Invoice Message", "item": "<PERSON><PERSON>", "link": "Link", "list": {"addOwner": "Add Owner", "reportMediaNotice": "Never logged in"}, "location": "Location", "mappingOwner": "Mapping Owner", "mappingOwnerInfo": "This email is already used for the following account(s). Please choose which account you wish to link to or click on \"Create New\" to create a new Prism account for this email address.", "noDiscount": "There is currently no discount.", "notFoundAccounts": "There is currently no account for this Owner.\nOnce their first invoice is created all accounts and payments will appear here.", "notFoundContent": "You currently have no Owner in your Owner list. To better manage your Stable and roles, be sure to add Owner and update their permissions.", "notFoundTitle": "There is currently no Owner", "note": "Note", "noteDetails": "Note details", "ok": "OK", "other": "Other Members", "ownerName": "Owner Name", "ownerNote": "Owner Note", "ownerNoteCreated": "Owner note has been created", "ownerNoteDeleted": "Delete success!", "ownerNoteEdited": "Owner note has been edited", "ownerSupport": "To enable Prism Finance and Billing, <NAME_EMAIL>", "percent": "Percent", "positiveOwnershipsInfo": "This Owner currently has shares in the following horses. Please confirm you wish to remove this owner.", "profile": {"addHorse": "Add Horse", "allOwners": "All Owners", "changeToSynd": "Change to Synd", "changesInHorseOwnership": "Changes in Horse Ownership", "createLoginInfo": "Create Login info", "editDisplayName": "Edit Display Name", "editOwner": "Edit Owner", "exportOwners": "Export Owners", "groupOwners": "Group Owners", "lastLoginTime": "Last login", "linkToTrainer": "<PERSON> To Trainer", "login": "<PERSON><PERSON>", "profile": "Profile", "reactivate": "Reactivate", "registered": "Registered", "remove": "Remove", "resendInvitation": "Resend Invitation", "resetPassword": "Reset Password", "suspend": "Suspend"}, "refreshPayment": "Refresh Payment", "refreshPaymentError": "Refresh payment status failed.", "remainingAmount": "Remaining Amount", "remainingAmountInfo": "Remaining Payment Amount: {date}", "remainingExpense": "Remaining Expenses", "remainingInvoices": "Remaining Invoices", "remainingInvoicesInfo": "You cannot remove this owner due to outstanding amounts/statements. To proceed with removal, please reconcile or void them in Xero, then sync to Prism. Once completed, you can remove this owner.", "remove": "Remove", "removeOwner": "Remove Owner", "resendInvitation": "Are you sure you want to resend invitation {name}?", "rule": "Rule", "search": {"placeholderInput": "Search Owner"}, "selectLocation": "Select Location", "selectOwner": "Select Owner", "selectStatus": "Select Status", "source": "Source", "status": "Status", "subject": "Subject", "suspendOwner": "Suspend Owner", "tableOwnership": {"addHorseAccess": "Add Horse Access", "approve": "Approve", "archivedHorse": "Archived Horse", "awaitingApproval": "Awaiting approval", "cancel": "Cancel", "delete": "Delete", "headerTable": {"action": "Action", "approveAll": "Approve All", "balance": "Balance", "date": "Date", "deselectAll": "Deselect All", "firstPurchased": "First Purchased", "generalTask": "General Task", "horse": "Horse", "lastSold": "Last Sold", "media": "Media", "noData": "There is currently no horse.", "noDataSup": "There is currently no previous horse.", "noDataSupOther": "There is currently no transaction.", "note": "Note", "present": "Present", "procedure": "Procedure", "purchased": "Purchased", "raceNoms": "Race (Noms)", "raceOthers": "Race (Others)", "rejectAll": "Reject All", "report": "Report", "selectAll": "Select All", "sold": "Sold", "transport": "Transport", "work": "Work"}, "horseAccessibility": "Horse Accessibility", "otherHorses": "Other Horses", "previousHorses": "Previous Horses", "reject": "Reject", "save": "Save", "tooltipArchived": "This horse is archived on Trainer Portal"}, "title": "Title", "to": "To", "tooltipDayrate": "Day Rate discounts are applied to the selected Day Rate ONLY. <br></br>Enter the Day Rate item you wish to discount and the % discount to be applied. <br></br>Once done, anytime the selected horse is charge the day rate, they will receive the relevant discount. <br></br>Note that this discount should apply to Locations and Status already set up in your  Location/Status presents and Day Rate Rules.", "updateHorseOwnership": "Update Horse Ownership", "updateOwnershipInfo": "Would you like to update ownership share for the horses related to this owner?<br></br>To update horse ownership, click Edit icon in Action for each horse.", "updated": "Updated", "xeroInvoiceNumber": "Xero Invoice Number"}, "payment": {"backToHomePage": "Back to home page", "directdebit": {"abn": "ABN", "address": "Address", "applicantDetails": "APPLICANT DETAILS", "companyName": "Company Name", "conditionMsg": "I hereby register with Thoroughbred Payments (operated by ZenPay Pty Ltd ABN ***********) and authorise Thoroughbred Payments (User ID 470911) or my merchant to process payments of recurring or varying amounts from my nominated card or bank account.<br></br><br></br>I confirm the information above is true and correct and that I have read, understood and agree to be bound by this Customer Registration Form (CRF) and Thoroughbred Payments' <termAndConditions>Terms & Conditions</termAndConditions> (TPTC).<br></br><br></br>I understand that this arrangement will remain in place until such time as it is cancelled by me, my merchant or Thoroughbred Payments, and all payment related queries or disputes will need to be resolved between me and my Merchant.<br></br><br></br>I also understand that transactions will appear on my card / bank statement as \"THOROUGHBRED PAYMENTS BALMAIN AU\".", "confirm": "Confirm", "country": "Country", "dateOfBirth": "Date of Birth", "directDebitRequestForm": "DIRECT DEBIT REQUEST FORM", "emailAddress": "Email Address", "firstName": "First Name", "lastName": "Last Name", "mobilePhone": "Mobile Phone", "paymentType": "Payment Type", "postcode": "Postcode", "setUp": "Set Up", "state": "State", "suburb": "Suburb", "successMsg": "Your Direct Debit Request has been processed successfully."}, "feesAndCharges": "Fees and Charges", "feesAndChargesContent": "Fees for accessing the Prism system maybe charged to certain Members where agreed. Charges are calculated on a monthly basis and based on the number of horses you have in the Prism system and the number of selected modules you are currently using.<br></br>These fees will charge for Services one month in arrears and automatically be deducted from the Members nominated account on the 1st day of the following month. Fees already paid will not be refunded should the Member choose to close the Account, change the number of horses or adjust the number of number of Prism modules.<br></br>To cancel or modify your Prism package, please email <email><EMAIL></email> or call 1800PRISMHQ (***********).<br></br>Members accept that should the Fees not be paid on time and are still in arrears after 7 days, Prism reserves the right to suspend or terminate the Member's Account until any arrears have been received.<br></br>Members acknowledge that Prism may alter or charge the fees schedule at any time subject to Prism provide a minimum of one months notice. Members are assumed to have accepted such Fee adjustment if it does not notify Prism to the contrary or cancel its Account and cease use of the Service within the one month notice period.", "general": "General", "generalContent": "Thank you for visiting Prism (herein referred to as (\"We,\" \"Prism,\" \"Site,\" or \"Service\") owned by Prism Pay Pty Ltd (ACN *********) with an address of 823/1 Queens Road, Melbourne, VIC, 3004. These Terms of Service (\"Terms\") govern your access to and use of the services and websites, mobile apps (the \"Services\"), and any information, text, graphics, photos or other materials uploaded, downloaded or appearing on the Services (collectively referred to as \"Content\"). Your access to and use of the Services is conditioned on your acceptance of and compliance with these Terms. By accessing or using the Services you agree to be bound by these Terms.<br></br>You are responsible for your use of the Services, for any Content you post to the Services, and for any consequences thereof. The Content you submit, post, or display will be able to be viewed by other users of the Services and potentially in the future through third party services and websites.<br></br>You may use the Services only in compliance with these Terms and all applicable local, state, national, and international laws, rules and regulations. The Services that we provide are always evolving and the form and nature of the Services that we provide may change from time to time without prior notice to you. In addition, we may stop (permanently or temporarily) providing the Services (or any features within the Services) to you or to users generally and may not be able to provide you with prior notice. We also retain the right to create limits on use and storage at our sole discretion at any time without prior notice to you.<br></br>If you create an account and use our website and services, this implies you agree to our general terms and conditions.<br></br>You must become a member (\"Member\") and you may asked in some cases to pay a monthly subscription fee, to be granted access to the Services. Any fees are due as agreed between you and us. Late fees and other penalties may apply. The information provided by you must be true, accurate, current and complete information. If we believe or suspect that your information is not true, accurate, current or complete, we may deny or terminate your access to the Site or Services (or any portion thereof). Member includes anyone who uses the Site for any reason.<br></br>To become a Member, you may provide your email address and select a password (in either event, your \"Account Credentials\"), which you may not transfer to or share with any third parties. If someone accesses our Site or Services using your Account Credentials, we will rely on those Account Credentials and will assume that it is really you or your representative who is accessing the Site and Services. You are solely responsible for any and all use of your Account Credentials and all activities that occur under or in connection with your Account Credentials. We do not share your Account Credentials in order to protect the privacy of your information and ensure that only persons you have authorised may have access to your information. We reserve the right to provide your Account Credentials to comply with any legal or enforcement proceeding. Without limiting any rights which we may otherwise have, we reserve the right to take any and all action, as it deems necessary or reasonable, to ensure the security of the Site and your Account Credentials, including without limitation terminating your access, changing your password, or requesting additional information to authorise activities related to your Account Credentials. You agree to be responsible for any act or omission of any users that access the Site or Services under your Account Credentials that, if undertaken by you, would be deemed a violation of these Terms of Use. In no event and under no circumstances will Prism be held liable to you for any liabilities or damages resulting from or arising out of (i) any action or inaction of Prism under this provision, (ii) any compromise of the confidentiality of your Account Credentials, and (iii) any unauthorised access to or use of your Account Credentials. Please notify us immediately if you become aware that your Account Credentials are being used without authorisation.<br></br>All content, whether publicly posted or privately transmitted, is the sole responsibility of the person who originated such Content. We may not monitor or control the Content posted via the Services and, we cannot take responsibility for such Content. Any use or reliance on any Content or materials posted via the Services or obtained by you through the Services is at your own risk.<br></br>We do not endorse, support, represent or guarantee the completeness, truthfulness, accuracy, or reliability of any Content or communications posted via the Services or endorse any opinions expressed via the Services. You understand that by using the Services, you may be exposed to Content that might be offensive, harmful, inaccurate or otherwise inappropriate, or in some cases, postings that have been mislabeled or are otherwise deceptive. Under no circumstances will we be liable in any way for any Content, including, but not limited to, any errors or omissions in any Content, or any loss or damage of any kind incurred as a result of the use of any Content posted, emailed, transmitted or otherwise made available via the Services or broadcast elsewhere.<br></br>You understand and acknowledge that the software, code, proprietary methods and systems used to provide the Site or Services (\"Our Technology\") are: (i) copyrighted by us and/or our licensors under Australian and international copyright laws; (ii) subject to other intellectual property and proprietary rights and laws; and (iii) owned by us or our licensors. Our Technology may not be copied, modified, reproduced, republished, posted, transmitted, sold, offered for sale, or redistributed in any way without our prior written permission and the prior written permission of our applicable licensors. You must abide by all copyright notices, information, or restrictions contained in or attached to any of Our Technology. Nothing in these Terms of Use grants you any right to receive delivery of a copy of Our Technology or to obtain access to Our Technology except as generally and ordinarily permitted through the Site according to these Terms of Use. Furthermore, nothing in these Terms of Use will be deemed to grant, by implication, estoppel or otherwise, a license to Our Technology. Certain of the names, logos, and other materials displayed on the Site or in the Services constitute trademarks, tradenames, service marks or logos (\"Marks\") of Prism or other entities. You are not authorised to use any such Marks. Ownership of all such Marks and the goodwill associated therewith remains with us or those other entities. Any use of third party software provided in connection with the Site or Services will be governed by such third parties' licenses and not by these Terms of Use.<br></br>We may not always be able to enforce these rules and you may be exposed through the Site or Services to Content that violates our policies or is otherwise offensive. You access the Site and Services at your own risk. We may, but are not obligated to, remove Content from the Site for any reason, including if we determine or suspect that such Content violates these Terms of Use. We are merely acting as a passive conduit for such distribution and we take no responsibility for your exposure to Content on the Site or through the Services whether it violates our content policies or not.<br></br>The Services may include advertisements, which may be targeted to the Content or information on the Services, queries made through the Services, or other information. The types and extent of advertising by us on the Services are subject to change. In consideration for us granting you access to and use of the Services, you agree that we and our third party providers and partners may place such advertising on the Services or in connection with the display of Content or information from the Services whether submitted by you or others.", "generalRulesOfUserConduct": "General rules of user conduct", "generalRulesOfUserConductContent": "It is our goal to make access to our Site and Services a good experience for all of our users. With that in mind we provide you our opinion of rules to follow when using the Site and Services: We are not responsible for user actions or policing user activity. The following activities are prohibited on the Site and Services. Accordingly you agree that you will not:<br></br>Conduct or promote any illegal activities while using the Site or Services;<br></br>Upload, distribute or print anything that may be harmful to minors;<br></br>Attempt to reverse engineer or jeopardize the correct functioning of the Site, or otherwise attempt to derive the source code of the software (including the tools, methods, processes, and infrastructure) that enables or underlies the Site;<br></br>Attempt to gain access to secured portions of the Site or Services to which you do not possess access rights;<br></br>Upload or transmit any form of virus, worm, Trojan horse, or other malicious code;<br></br>Use the Site or Services to generate unsolicited email advertisements or spam;<br></br>Use the Site or Services to stalk, harass or harm another individual;<br></br>Take any action that may undermine the feedback or ratings systems;<br></br>Use any high volume automatic, electronic or manual process to access, search or harvest information from the Site or Services (including without limitation robots, spiders or scripts);<br></br>Interfere in any way with the proper functioning of the Site and Services or interfere with or disrupt any servers or networks connected to the Site or Services, or disobey any requirements, procedures, policies or regulations of networks connected to the Site or Services;<br></br>Use any robot, spider, other automatic device, or manual process to extract, \"screen scrape,\" monitor, \"mine,\" or copy any static or dynamic web page on the Site or the Content contained on any such web page for commercial use without our prior express written permission;<br></br>Impersonate any person or entity, or otherwise misrepresent your affiliation with a person or entity;<br></br>Mirror or frame the Site or any Content, place pop-up windows over its pages, or otherwise affect the display of its pages;<br></br>Not to use the Service including any Content, in breach of any legislation or regulation at all, to include, without limitation, relevant legislation and regulation concerning and governing gaming and the horse racing and horse training industries; or<br></br>Not to use the Service as a means to facilitate a gambling or other betting transaction or service either as a Wagering Operator or otherwise.<br></br>In using certain Services, you authorise us to act on your behalf to access and interact with social networking sites such as Facebook and Twitter (any such site, a \"SN Site\") to retrieve information from, and/or submit information to, such SN Sites at your request. We will not collect your username and password, and we will instead store the unique authorisation code (or a \"token\") provided to us by the SN Site to access it on your behalf. You can revoke our access to an SN Site at any time by amending the appropriate settings from within your account settings on that site. You should note that an SN Site may change or amend its guidelines and our access to it at any time, and we cannot guarantee that our Services will always include a connection to such SN Site.", "msg1": "All payments will appear on your statement as being to Prism Pay Pty Ltd.", "msg2": "Prism Pay Pty Ltd, 823/1 Queens Road, Melbourne, VIC, 3004", "outstandingAmount": "Outstanding amount", "payeeOrOrganisation": "Payee/Organisation", "paymentSuccessMsg": "Your payment has been processed successfully.", "paymentSuccessWithMoneyMsg": "You have successfully made a payment of ${value}", "paymentTo": "Payment to {value}", "requestSuccess": "Your Prism ID has been submitted successfully.", "success": "Success", "terms&Conditions": "Terms & Conditions", "termsAndConditions": "Terms and conditions can be found here .", "thankYou": "THANK YOU!", "unsuccessWithMoneyMsg": "and failed to make a payment of ${value}"}, "plus": {"communication": {"comingSoon": "Coming Soon", "draft": "Draft", "draftAndSchedule": "Draft & Scheduled", "groupUpdate": "Group Update", "headerTable": {"action": "Action", "date": "Date", "elapsedDays": "Elapsed Days", "horse": "Horse", "location": "Location", "note": "Note", "status": "Status", "subject": "Subject", "type": "Type"}, "history": "Last 3 Months", "inputNote": "Input Note", "lastUpdate": "Last Updates", "media": "media", "noCommFound": "No Communications found.", "noData": "There are currently no {name}.", "noDataCommunication": "Sorry, there are no results that match your search.", "report": "report", "scheduled": "Scheduled", "stableUpdate": "Stable update"}, "headerNav": {"communication": "Communications", "filter": {"flag": "Flag", "updatedToDay": "Updated Today"}, "finance": "Finance", "horse": "Horses", "industry": "Industry", "operations": "Operations", "owner": "Owner"}, "horse": {"headerTable": {"barnBox": "Barn - Box", "bloodProfile": "Blood Profile", "feedLeft": "Feed Left", "general": "General", "generalNote": "General Note", "horse": "Horse", "location": "Location", "procedure": "Procedure", "scope": "<PERSON><PERSON>", "status": "Status", "temp": "Temp (°C)", "trotUp": "Trot Up", "vetNote": "Vet Note", "wgt": "Wgt", "work": "Work", "xray": "X-ray"}, "noData": "There are currently no Horses.", "notDataTitle": "No Horses found", "recurringTask": "Recurring task", "search": "Search", "time": {"aDay": "1 day ago", "today": "Today", "tomorrow": "Tomorrow", "yesterday": "Yesterday"}, "viewMore": "View more"}, "messageOverlayBottom": "To find out more about Prism Plus or to go subscribe, go to ", "messageOverlayMiddle": "If your organization has already to subscribed ask your administrator to grant you access.", "messageOverlayTop": "Prism Plus has many enhanced features and requires an addition subscription."}, "praForm": {"changeOfOwner": {"additionalFee": {"label": "Additional Horse (if applicable)", "placeholder": "Insert Additional Horse"}, "applicableStatesNote": "(Applicable NSW, ACT, VIC, SA, NT & TAS only)", "asManagingOwnerI": "As Managing Owner I,", "auctioneerName": {"label": "Auctioneer’s Name", "placeholder": "Input Auctioneer's Name"}, "auctioneerNote": "Please ensure the Auctioneer is on the PRA approved list and that this form is stamped with the Auctioneer Company Name & detail.", "bankDetailsNote": "Tick box if you have previously provided bank details for this horse only and they have not changed", "company": {"label": "Company", "placeholder": "Input company"}, "dateOfTransfer": "Date of transfer of ownership/Date sold", "daytimePhone": {"label": "Daytime Phone", "placeholder": "Insert Daytime Phone"}, "deliveryInstructionNote": "If the Certificate of Transfer is not to be forwarded to the Manager, please supply delivery instruction below:", "managingOwnerChangeNote": "Only to be completed when the Managing Owner is changing, or where the share percentage of the existing Managing Owner is changing in anyway.", "newOwnerDetails": "New owner details - Owner {number}", "outgoingAcknowledgement": "acknowledge the details of the outgoing Transfer of Ownership below to be true and correct and confirm I have notified all remaining owners (being those owners who are neither relinquishing nor acquiring a share in a horse), if any, of the transfer(s) stated on those forms.", "paymentNote": "Payment options include cheque, money order, or credit card. Please make cheques and money orders payable to the relevant Principal Racing Authority.", "printName": "Print Name", "purchasePrice": {"label": "Purchase Price", "placeholder": "Value of your share"}, "recipient": {"label": "Recipient", "placeholder": "Insert Recipient"}, "relinquishingNote": "Only owner relinquishing their share, or part thereof, in this horse must be listed below.", "salePrice": {"label": "Sale Price", "placeholder": "Insert sale price"}, "splitPrizeQuestion": "Do you require prize money to be split between owners where bank accounts are supplied?", "totalPayment": {"label": "Total Payment", "placeholder": "Insert Total Amount"}, "transferFee": {"description": "The fee to transfer the ownership of a horse is: $110", "label": "Transfer of Ownership Fee", "placeholder": "Insert transfer of ownership fee"}, "witnessName": "Witness Name"}, "clearAll": "Clear All", "confirmClear": "All data will be cleared. Are you sure you want to continue?", "confirmIncompletePrint": "You have not filled all required fields. Are you sure want to continue?", "dam": {"label": "Dam", "placeholder": "Input Dam"}, "horseDescription": "Horse Description", "horsesPurchasedAtAnAuction": "Horses Purchased at an Auction", "informationAndInstruction": "Information and Instruction", "inputName": "Input Name", "managingOwnerDeclaration": "Managing Owner's Declaration", "nav": {"changeOfContactDetails": "Change of Contact Details", "changeOfManager": "Change of Manager", "changeOfOwner": "Change of Owner", "changeOfShare": "Change of Share", "registrationForm": "Registration Form"}, "navigateTo": "Navigate to:", "newManagingOwner": "New Managing Owner", "newManagingOwnerDetails": "New Managing Owner Details", "newOwner": "New Owner {number}", "outgoingOwners": "Outgoing Owners", "paymentDetails": "Payment Details", "pleasEnterOwnerEmail": "Please enter owner's email", "print": "Print", "registrationForm": {"colour": {"label": "Colour", "placeholder": "Input Colour"}, "country": {"label": "Country of Foaling", "placeholder": "Select Country"}, "currentHorseLocation": "Current Horse Location", "deliveryInstructions": {"address": "Address", "description": "ONLY complete this section if you want the horse's identification card to be sent to someone other than the managing owner.", "inputAddress": "Input Address", "inputMobile": "Input Mobile", "mobile": "Mobile", "name": "Name", "title": "Delivery Instructions"}, "foalYear": "Foal Year", "hasThisHorseBeenNamedInAnotherCountry": "Has this horse been named in another country?", "horseRegistration": "Horse registration", "microchipNumber": {"label": "Microchip Number", "placeholder": "Input Microchip Number"}, "owner": {"accountNumber": {"label": "Account Number", "placeholder": "Insert Account Number"}, "bankAccount": {"label": "Bank account name", "placeholder": "Insert Bank Account Name"}, "bankDetails": {"description": "Only complete bank details if split payment is required", "label": "Bank Details"}, "bsb": "BSB", "companyOrStud": "Company or Stud", "confirmDelete": "Are you sure you want to delete this Owner item?", "contactDetails": "Contact Details", "dateOfBirth": "Date of Birth", "declareYourGSTStatus": "Declare your GST status", "description": "When completing this section you are required to provide ALL the information requested below", "email": {"label": "Email", "placeholder": "Insert Email"}, "givenName": {"label": "Given Names of Owner", "placeholder": "Insert Give Name"}, "givenNamesOfCompanyStud": "Given Names of Company/Stud", "givenNamesOfRegisteredSyndicate": "Given Names of Registered Syndicate", "ifYesPleaseSupplyABNSubjectToValidation": "If yes, please supply ABN subject to validation", "individualOwner": "Individual Owner", "insertABN": "Insert ABN", "isLeased": "Tick this box if the horse is to be leased or will not be racing.", "isOwned": "Tick this box if the syndicate has owned horses previously and you wish to add this horse to the syndicate.", "isThisEntityGSTRegisteredForRacingPurposes": "Is this entity GST registered for racing purposes?", "label": "Managing Owner", "mobilePhone": {"label": "Mobile Phone", "placeholder": "Insert Mobile Phone"}, "noIAmAHobbyist": "No - I am a hobbyist", "other": {"label": "If other, please specify", "placeholder": "Insert Specify"}, "pleaseTick": "Please Tick", "postalAddress": {"label": "Postal Address", "placeholder": "Insert Postal Address"}, "registeredSyndicate": "Registered Syndicate", "selectOwner": "Select Owner", "share": {"label": "Share", "placeholder": "Input Share"}, "surname": {"label": "Surname of Owner", "placeholder": "Insert Surname"}, "surnameOfCompanyStud": "Surname of Company/Stud", "surnameOfRegisteredSyndicate": "Surname of Registered Syndicate", "title": {"miss": "Miss", "mr": "Mr.", "mrs": "Mrs.", "ms": "Ms.", "other": "Other"}}, "payment": {"byExpressPost": "$8 by Express Post", "description": "The fee to register a horse is $110.00. Registered Syndicates Only - An additional $40.00 is payable by each registered syndicate with an interest in the horse except if it is the first horse to be registered in the syndicate's name.", "freeByStandardPost": "Free by Standard Post", "inputFeeAmount": "Input Fee Amount", "paymentOptions": "Payment options include cheque, money order, or credit card. Please make cheques and money orders payable to the Registrar", "registeredSyndicateFee": "Registered Syndicate Fee (if applicable)", "registrationFee": "Registration Fee", "selectHowWouldLikeToReceiveYourIDCard": "Select how would like to receive your ID Card?", "title": "Payment", "totalAmount": "Total Amount"}, "postcode": {"label": "Postcode", "placeholder": "Input Postcode"}, "proposedName": {"description": "Please provide three names in order of preference.", "doNotIncludeUnwantedNames": "Do not include unwanted names", "listItem": {"checkName": "To check name availability and to view the Registrar's naming guidelines, visit <link>www.racingaustralia.horse</link>. To be used as a guide only. All names are subject to the approval of the Registrar of Racehorses.", "nameChangesCharges": "Name changes and amendments are subject to additional charges.", "nameLengthLimit": "Names must not exceed 18 characters including spaces and apostrophes.", "nameRepeatRule": "A name can not be repeated for 17 years after the year of birth of a house common law trade name, or for 20 years after the year of birth of the youngest named produce of a horse with the same name.", "noBusinessNames": "Registered company or business names and registered, pending or common law trade marks will not be accepted.", "noFullName": "First name and surname combinations are not permitted e.g. <PERSON>.", "offensiveNotPermitted": "Offensive or suggestive names will not be permitted.", "similarNameRestriction": "Names that are similar to a name with an existing name restriction may not be permitted."}, "note": "* Completed applications should be lodged, complete, 10 working days prior to the horse being nominated to trial or race.", "placeholder": "Input the name", "title": "Proposed Name"}, "sex": "Sex", "state": {"label": "State", "placeholder": "Input State"}, "streetAddress": {"label": "Street Address", "placeholder": "Input Street Address"}, "suburb": {"label": "Suburb", "placeholder": "Input Suburb"}, "theFoalIdentificationCardMustBeLodgedWithTheApplication": "The Foal Identification Card must be lodged with the application.", "yesIHaveEnclosedTheFoalIdentificationCard": "Yes, I have enclosed the Foal Identification Card."}, "selectHorse": "Select Horse", "sendEmail": "Send Email", "sire": {"label": "<PERSON>e", "placeholder": "Input Sire"}, "suffix": {"label": "Suffix", "placeholder": "Input Suffix"}}, "racing": {"detail": {"allRaceButton": "All Acc/Noms", "emptyDataTitle": "No found record"}, "formStats": {"AtThisDistance": "AT THIS DISTANCE", "AtThisWeight": "AT THIS WEIGHT", "BarrierAndTrack": "BARRIER & TRACK", "JockeyAndDistance": "JOCKEY & DISTANCE", "JockeyDistanceAndTrainer": "JOCKEY DISTANCE & TRAINER", "JockeyDistanceAndVenue": "JOCKEY DISTANCE & VENUE", "JockeyDistanceTrainerAndVenue": "JOCKEY DISTANCE TRAINER & VENUE", "ThisTrack": "THIS TRACK", "TrackAndDistance": "TRACK & DISTANCE", "TrainerAndDistance": "TRAINER & DISTANCE", "WithThisJockey": "WITH THIS JOCKEY"}, "headerTable": {"accept": "Accept", "action": "Action", "bar": "Bar", "date": "Date", "distance": "Distance", "driver": "Driver", "gear": "Gear", "horse": "Horse", "horseName": "Horse Name", "horseNo": "Horse No.", "horseNumber": "Horse Number", "jockey": "<PERSON><PERSON>", "last5": "Last 5", "mrg": "Mrg", "no": "No", "pos": "Pos", "procedureCaution": "Procedure Caution", "race": "Race", "raceNo": "Race No.", "raceStats": "Race Details / Stats", "result": "Result", "scratch": "<PERSON><PERSON><PERSON>", "sp": "SP", "startingOdds": "Starting Odds", "track": "Track", "trainer": "Trainer", "wgt": "Wgt"}, "pastRaces": {"emptyDataDescription": "There are no Race Results available from the past two weeks.", "emptyDataHarnessDescription": "There is currently no Racing history for this horse.", "emptyDataTitle": "No Recent Race Results found"}, "pringByDate": "Print by Date", "racing": "Racing", "tabs": {"previous2Weeks": "Previous 2 Weeks", "racePlanning": "Race Planning", "racingCalendar": "Racing Calendar", "upcomingRaces": "Upcoming Races"}, "upcomingRaces": {"emptyDataDescription": "Only horses that have Nominated or Accepted via Racing Australia will be displayed here.", "emptyDataHarnessDescription": "There is currently no Upcoming Races.", "emptyDataTitle": "No Upcoming Races found"}}, "report": {"communication": {"filter": {"horseName": "Horse Name", "horses": "Horses", "includeStableUpdates": "Include Stable Updates", "includeSyndicateUpdates": "Include Syndicate Updates", "owners": "Owners", "selectHorses": "Select Horses", "selectOwner": "Select Owner", "selectOwners": "Select Owners", "selectUpdateTo": "Select Update to", "updateTo": "Update To"}, "resendMail": {"confirmResendEmail": "Are you sure you want to resend this email?", "emailSendSuccessfully": "Email send successfully"}, "table": {"category": "Category", "dataNotFound": "Data not found", "date": "Date", "elapsedDays": "Elapsed Days", "horseName": "Horse Name", "location": "Location", "noDataSearch": "Sorry, there are no results that match your search.", "resend": "Resend", "resendEmail": "<PERSON><PERSON><PERSON>", "status": "Status", "subject": "Subject", "to": "To"}}, "emaiLog": {"filter": {"chooseStable": "Choose Stable", "date": "Date", "from": "From", "recipient": "Recipient", "selectStatus": "Select Status", "stable": "Stable", "status": "Status", "subject": "Subject", "to": "To", "trainer": "Trainer"}, "modal": {"from": "From", "status": "Status", "subject": "Subject", "to": "To"}, "notify": {"confirmReactiveEmail": "Are you sure you want to reactive {value}?", "confirmResendEmail": "Are you sure you want to resend {value}?", "emailReactiveSuccessfully": "Email reactive successfully", "emailSendSuccessfully": "Email send successfully", "theseEmails": "these emails", "thisEmail": "this email"}, "table": {"date": "Date", "forwardEmail": "Forward Email", "resend": "Resend", "resendEmail": "<PERSON><PERSON><PERSON>", "status": "Status", "subject": "Subject", "to": "To"}}, "headerNav": {"communication": "Communication", "communicationReport": "Communication Report", "emailLog": "<PERSON><PERSON>", "horse": "Horse Report", "horseOwnerExtract": "Horse Owner Extract", "horseSpecificReport": "Horse Specific Report", "horsesDailyReport": "Horses Daily Report", "horsesReviewReport": "Horses Review Report", "location": "Location - Status", "paddockReport": "Paddock Report", "raceResult": "Race Result", "raceResults": "Race Results", "stockCount": "Stock Count", "tBPPaymentsReport": "TBP Payments Report", "taskAndNote": "Task & Note", "tasksAndNotes": "Tasks & Notes"}, "horseFinance": {"filter": {"horseName": "Horse Name", "horsesByOwner": "Horses By Owner", "includeArchivedHorse": "Include Archived Horse", "includeOwnerZero": "Include Owner 0%", "location": "Location", "ownerName": "Owner Name", "ownersByHorse": "Owners By Horse", "selectHorses": "Select Horses", "selectLocation": "Select Location", "selectOwners": "Select Owners", "selectStatus": "Select Status", "status": "Status", "trainer": "Trainer"}, "paymentReportOn": "Payment report on", "sort": {"locationAsc": "Location (A-Z)", "locationDesc": "Location (Z-A)", "sortBy": "Sort By", "statusAsc": "Status (A-Z)", "statusDesc": "Status (Z-A)"}, "table": {"address": "Address", "category": "Category", "colour": "Colour", "commsEmail": "<PERSON><PERSON><PERSON>", "contactName": "Contact Name", "financeAccount": "Finance Account", "financeEmail": "Finance Email", "horseName": "Horse Name", "location": "Location", "mobile": "Mobile", "pedigree": "Pedigree", "percentage": "Percentage", "purchasePrice": "Purchase Price", "share": "Share", "status": "Status", "type": "Type"}}, "horseReport": {"export": {"groupByLocationAndBarn": " Group by Location & Barn", "groupByLocationAndBarnAndAge": " Group by Location, Barn & Age", "noGroup": "No Group"}, "filter": {"category": "Category", "date": "Date", "endDate": "End Date", "feedLeft": "Feed Left", "fromDate": "From Date", "horseName": "Horse Name", "include": "Include", "location": "Location", "saturday": "Saturday", "selectCategory": "Select Category", "selectHorseName": "Select Horse Name", "startDate": "Start Date", "sunday": "Sunday", "temperature": "Temperature", "toDate": "To Date", "trainer": "Trainer", "trotUp": "Trot Up", "vetNote": "Vet Note", "weight": "Weight"}, "table": {"age": "Age", "barn": "<PERSON>n", "bonusScheme": "Bonus Scheme", "box": "Box", "brand": "Brand", "color": "Color", "dam": "Dam", "daysOfWork": "Days of Work", "horse": "Horse", "horsesDailyReport": "Horses Daily Report", "inputNote": "Input Note", "lastRace": "Last Race", "location": "Location", "nameOfSale": "Name of Sale", "noDataSearch": "Sorry, there are no results that match your search.", "note": "Note", "pedigree": "Pedigree", "rating": "Rating", "sex": "Sex", "sire": "<PERSON>e", "status": "Status", "trainer": "Trainer"}}, "locationStatus": {"filter": {"current": "Current", "date": "Date", "from": "From", "historical": "Historical", "horseName": "Horse Name", "includeArchivedHorse": "Include Archived Horse", "location": "Location", "selectDate": "Select Date", "selectHorses": "Select Horses", "selectLocation": "Select Location", "selectStatus": "Select Status", "status": "Status", "to": "To", "trainer": "Trainer"}, "table": {"barn": "<PERSON>n", "box": "Box", "duration": "Duration", "from": "From", "horse": "Horse", "location": "Location", "note": "Note", "noteSuccessfullyUpdated": "Note successfully updated", "placeholderNote": "Input Note", "print": "Print", "status": "Status", "to": "To", "totalToPresent": "Total to present", "trainer": "Trainer"}}, "paddock": {"filter": {"barnPaddock": "Barn/Paddock", "broodmares": "Broodmares", "broodmaresWithFoals": "Broodmares with Foals", "byHorseType": "By Horse Type", "byOwner": "By Owner", "category": "Category", "horseType": "Horse Type", "location": "Location", "ownerName": "Owner Name", "selectBarn": "Select Barn", "selectCategory": "Select Category", "selectHorseType": "Select Horse Type", "selectLocation": "Select Location", "selectOwners": "Select Owners", "total": "Total"}, "table": {"barnPaddock": "Barn/Paddock", "bookedTo": "Booked To", "box": "Box", "brands": "Brands", "category": "Category", "clickToViewOwnerList": "Click to view Owner List", "colour": "Colour", "dob": "DOB", "due": "Due", "foalCS": "Foal C/S", "foalSire": "Foal <PERSON><PERSON>", "horseName": "Horse Name", "horseType": "Horse Type", "lastServedBy": "Last Served By", "location": "Location", "lsd": "LSD", "mareName": "Mare Name", "name": "Name", "owner": "Owner", "ownerList": "Owner List", "sex": "Sex", "shareholding": "Shareholding"}}, "payment": {"filter": {"fromDate": "From Date", "ownerName": "Owner Name", "selectOwner": "Select Owner", "toDate": "To Date"}, "table": {"amount": "Amount", "email": "Email", "ownerAccount": "Owner Account", "paymentDate": "Payment Date", "totalAmount": "Total Amount", "warning": "Listed below are all of the payments you have received via Thoroughbred Payments. All other payments such as EFT, Bank transfers will not be shown here."}}, "raceResults": {"filter": {"from": "From", "to": "To"}, "table": {"action": "Action", "classOfRace": "Class of Race", "date": "Date", "distance": "Distance", "driver": "Driver", "horse": "Horse", "horseName": "Horse Name", "horseNo": "Horse No.", "jockey": "<PERSON><PERSON>", "mrg": "Mrg", "position": "Position", "prize": "Prize", "race": "Race", "raceDate": "Race Date", "raceNo": "Race No.", "result": "Result", "sp": "SP", "time": "Time", "track": "Track", "trainer": "Trainer", "wgt": "Wgt"}}, "taskNote": {"filter": {"assignedTo": "Assigned To", "barn": "<PERSON>n", "category": "Category", "description": "Description", "duration": "Duration", "from": "From", "gear": "Gear", "horseName": "Horse Name", "inputDescription": "Input Description", "location": "Location", "method": "Method", "month": "Month", "postWork": "Post Work", "prework": "Prework", "reason": "Reason", "selectAssignees": "Select assignees", "selectBarn": "Select barn", "selectCategory": "Select category", "selectGear": "Select gear", "selectHorses": "Select horses", "selectLocation": "Select location", "selectMethod": "Select method", "selectPostWork": "Select post work", "selectPrework": "Select prework", "selectReason": "Select reason", "selectSpecific": "Select specific", "selectStatus": "Select status", "selectStatuses": "Select statuses", "selectTasks": "Select tasks", "selectWork": "Select work", "shareBy": "Shared by", "shareFrom": "Shared From", "specific": "Specific", "status": "Status", "taskAndNote": "Tasks & Notes", "to": "To", "trainer": "Trainer", "work": "Work"}, "table": {"allTasks": "All Tasks", "assignedTo": "Assigned To", "barn": "<PERSON>n", "bookedTo": "Booked To", "box": "Box", "comment": "Comment", "daySince": "Day(s) Since", "dentist": "Dentist", "description": "Description", "duration": "Duration", "farrier": "<PERSON><PERSON>", "fged": "Fged", "flag": "Flag", "foal": "Foal", "from": "From", "gear": "Gear", "horse": "Horse", "horseNotes": "Horse Notes", "horseStatus": "Horse Status", "inputComment": "Input Comment", "location": "Location", "mare": "Mare", "measure": "Measure", "note": "Note", "noteSuccessfullyUpdated": "Note successfully updated", "other": "Other", "others": "Others", "ov": "Ov", "placeholderNote": "Input Note", "procedure": "Procedure", "record": "Record", "recurringTask": "Recurring task", "srv": "Srv", "status": "Status", "time": "Time", "to": "To", "trainer": "Trainer", "transport": "Transport", "type": "Type", "work": "Work"}}}, "schedule": {"calendar": {"action": "Action", "actions": {"general": "Add General", "note": "Add Note", "procedure": "Add Procedure", "transport": "Add Transport", "work": "Add Work"}, "assignedTo": "Assigned To: {assignee}", "confirmations": {"deleteGeneral": {"message": "Are you sure you want to delete this task?", "title": "Warning!", "withPermissionMessage": "You are about to delete a task that may impact your transactions and owner billings. Are you sure you want to continue?"}, "deleteTrackwork": {"message": "Are you sure you want to delete this Trackwork?", "title": "Warning!"}}, "modals": {"createGeneral": "Create Task Schedule", "createNote": "Create Note", "createProcedure": "Create Procedure Schedule", "createTrackwork": "Create Work Schedule", "createTransport": "Create Transport Schedule", "crmTask": "CRM Task", "editGeneral": "Edit Task Schedule", "editNote": "Edit Note", "editTrackwork": "Edit Work Schedule"}, "multipleHorses": "Multiple Horses ({count})", "multipleProcedures": "Multiple Procedures ({count})", "noStaff": "No Staff", "notifications": {"deleteCRMTask": {"success": "CRM task successfully deleted"}, "deleteGeneral": {"success": "General task successfully deleted"}, "deleteTrackwork": {"success": "Trackwork deleted successfully"}}, "racePosition": "{position, selectordinal, one {#st} two {#nd} few {#rd} other {#th}}", "transportFromTo": "From {from} To {to}"}, "crm": {"assignedTo": "Assigned to", "comment": "Comment", "crmTask": "CRM Task", "deleteMessage": "Are you sure you want to delete this task?", "deleteSuccessMsg": "CRM task successfully deleted", "description": "Description", "owner": "Owner", "setReminder": "<PERSON>minder", "status": "Status", "trainer": "Trainer"}, "feed": {"addFeed": "<PERSON><PERSON> Feed", "barnBox": "Barn-Box", "comment": "Comment", "confirmDeleteFeed": "Are you sure you want to delete this horse feed?", "feed": "Feed", "horse": "Horse", "location": "Location", "supplement": "Supplement", "tally": "<PERSON><PERSON>", "time": "Time"}, "general": {"addTask": "Create Task Schedule", "assignedTo": "Assigned To", "barnBox": "Barn-Box", "cantDeleteMessage": "There {count, plural, =0 {is # task} =1 {is 1 task} other {are # tasks}} can't be deleted.", "category": "Category", "clickToViewHorseList": "Click to view Horse List", "comment": "Comment", "deleteMultipleTaskNormalWarning": "Are you sure you want to delete {count, plural, one {this task} other {these # tasks}}?", "deleteMultipleTaskWarningByCondition": "You are about to delete tasks that may impact your transactions and owner billings. Are you sure you want to continue?", "deleteSuccess": "You have deleted {taskD<PERSON>ted<PERSON><PERSON>, plural, =1 {1 task} other {# tasks}} successfully", "deletedSuccessGeneral": "General {taskDeletedNum, plural, =1 {task} other {tasks}} successfully deleted!", "description": "Description", "multipleHorses": "Multiple Horses [{number}]", "newNote": "New Note", "newTask": "New Task", "noData": "There is currently no Task scheduled {date}", "other": "Other", "printIncludeBrandInfo": "Print includes brand info", "status": "Status", "taskSchedule": "Task Schedule", "trainer": "Trainer", "unassigned": "Unassigned", "updateCompletionTimeSuccess": "Completion time successfully updated!"}, "header": {"nav": {"crmTask": "CRM Task", "expense": "Expense", "feed": "Feed", "general": "General", "procedure": "Procedure", "transport": "Transport", "work": "Work", "worksheet": "Worksheet"}, "today": "Today", "views": {"calendar": "Calendar View", "lifetime": "Lifetime View", "task": "Task View"}}, "note": {"placeholder": "Input Note"}, "procedure": {"assignedTo": "Assigned to", "attachLinkSuccess": "Work now attached to Procedure.", "attachToWork": "Attach To Work", "barnBox": "Barn-Box", "brand": "Brand", "cannotCopy": "Can not copy task", "clickViewHorse": "Click to view Horse List", "comment": "Comment", "confirmDeleteTreatment": "Are you sure you want to delete {data, plural, =1 {this scheduled task?} other {these scheduled tasks?}}", "confirmDeleteWork": "Are you sure you want to delete this work?", "confirmNoWork": "This Work entry has no actual work detailed. Do you wish to continue?", "confirmToLink": "Are you sure you want to link ?", "copySuccess": "Tasks successfully copied", "copyToLabel": "Please select a date that you would like to copy to", "deleteAllTreatment": "You are about to delete tasks that may impact your transactions and owner billings. Are you sure you want to continue?", "deleteSuccess": "You have deleted {number} task successfully", "deleteWorkFail": "You must unlink this Horse to delete this task", "errorCreateTrackwork": "There is already an identical task for this horse on this date. Are you sure you want to add another duplicate Task?", "errorSelect": "Please select at least an item.", "gear": "Gear", "horse": "Horse", "linkWork": "Link work", "location": "Location", "multipleHorses": "Multiple Horses ({num})", "noProcedure": "There is currently no Procedure scheduled for {date}", "noSelect": "Please select at least one task to delete", "other": "Other", "procedure": "Procedure", "recurringTask": "Recurring task", "removeNote": "Are you sure you want to delete this Note?", "removeWork": "Are you sure you want to remove ?", "removeWorkSuccess": "Work now removed from Procedure.", "selectGear": "Select Gear", "selectTack": "Select Tack", "status": "Status", "tack": "Tack", "time": "Time", "trainer": "Trainer", "unLinkWorkSuccess": "Procedure has now been deleted.", "unassigned": "Unassigned", "warningSelectOneTask": "Please select one task.", "work": "Work"}, "transport": {"assignedTo": "Assigned to", "barnBox": "Barn-Box", "cannotCopy": "Can not copy task", "clickViewHorse": "Click to view Horse List", "comment": "Comment", "confirmDelete": "Are you sure you want to delete {count, plural, =1 {this task} other {these tasks}}?", "confirmDeleteCompletedMsg": "You are about to delete tasks that may impact your transactions and owner billings. Are you sure you want to continue?", "copySuccess": "Tasks successfully copied", "copyToLabel": "Please select a date that you would like to copy to", "deleteBulkErrorMessage": "There are {value} tasks can't be deleted.", "deleteBulkSuccessMessage": "You have deleted {value} tasks successfully", "from": "From", "multipleHorses": "Multiple Horses ({num})", "newTransport": "New Transport", "noData": "There is currently no Transport scheduled for {date}", "originalTooltipLabel": "Original Transport of task on {date}", "returnTripTooltipLabel": "Returning Transport from task on {date}", "status": "Status", "time": "Time", "to": "To", "trainer": "Trainer", "vehicle": "Vehicle"}, "work": {"actionTooltips": {"deleteDisabled": "You must unlink this Horse to delete this task"}, "actions": {"addNote": "Add Note", "addWork": "Add Work", "bulkAction": "Bulk Action", "copy": "Copy", "delete": "Delete", "group": "Group", "link": "Link", "moveTo": "Move to", "newProcedure": "New Procedure", "print": "Print", "ungroup": "Ungroup", "unlink": "Unlink", "viewAsPdf": "View as PDF"}, "brandInfoSwitch": "Print includes Brand info", "errorSelect": "Please select at least an item.", "filters": {"placeholders": {"barns": "<PERSON><PERSON>", "groups": "Select", "locations": "Filter Location"}}, "groupsManagement": {"groupNamePlaceholder": "Group Name", "modalTitle": "Update Group", "position": "Position"}, "hint": {"description": "The drag & drop and inline editing feature only supported when you select a specific location.", "title": "Hint"}, "modals": {"copy": {"dateLabel": "Please select a date that you would like to copy to"}, "link": {"content": "Please select the Horse(s) that you would like this Horse to train with:", "horsesPlaceholder": "Horse", "title": "Link to Horse {horse}"}}, "print": {"barnBox": "Barn-Box", "brand": "Brand", "comment": "Comment", "gear": "Gear", "horse": "Horse", "jockey": "<PERSON><PERSON>", "other": "Other", "work": "Work"}, "table": {"arioneo": {"actions": {"detach": "<PERSON><PERSON>", "view": "View"}, "columns": {"time": "Time"}}, "etrakka": {"actions": {"detach": "<PERSON><PERSON>", "view": "View", "viewLess": "View less", "viewMore": "View more"}, "labels": {"actions": "Actions", "duration": "Duration", "time": "Time"}}, "treatment": {"actions": {"removeFromWork": "Remove from Work"}, "cells": {"unassigned": "Unassigned"}, "columns": {"comment": "Comment", "details": "Procedure", "dueDate": "Time", "reason": "Reason", "staffName": "Assigned To", "status": "Status"}}, "work": {"actions": {"delete": "Delete", "edit": "Edit", "horseNote": "Horse Note", "link": "Link", "linkProcedure": "Link Procedure", "more": "More", "setReminder": "<PERSON>minder", "unlink": "Unlink", "view": "View"}, "columns": {"barnBox": "Barn - Box", "comment": "Comment", "gears": "Gear", "horseName": "Horse ({count})", "otherWorks": "Other", "staffName": "Assigned To", "tacks": "Tack", "trainer": "Trainer", "work": "Work"}, "inlineEdit": {"placeholders": {"advisor": "Input Name or Email", "assignee": "<PERSON><PERSON>", "comment": "Input Comment", "gear": "Select Gear", "horse": "Select Horse", "location": "Select Location", "other": "Other", "postWork": "Post-Work", "preWork": "Pre-Work", "tack": "Select Tack", "work": "Work"}}, "tooltips": {"groupName": "Click to edit {groupName}"}}}, "tooltips": {"linkMinSelected": "You should select at least 2 tasks to make a link", "linkSameWork": "You can only link the horses that take the same work"}, "workPerfomation": {"arioneoRequestAccess": "Arioneo Request Access", "avghr2_5m": "AvgHR2_5m", "b200": "B200", "b400": "B400", "b600": "B600", "b800": "B800", "bPM120s": "BPM120s", "bPM300s": "BPM300s", "bPM60s": "BPM60s", "confirmDeleteWorkArioneo": "Are you sure you want to remove?", "date": "Date", "doYouWantToApplyThisSettingToNextTimeView": "Do you want to apply this setting to next time view?", "duration": "Duration", "g45mtrs": "G45Mtrs", "g45time": "G45Time", "g60mtrs": "G60Mtrs", "g60time": "G60Time", "hRMax": "HRMax", "intervals": "Intervals", "link": "Link", "mj": "MJ", "noWorkData": "There is currently no Performance for this Horse", "note": "Note", "performance": "Performance", "pleaseInputYourArioneoEmail": "Please input your Arioneo email", "sF60": "SF60", "sFMax": "SFMax", "sL50": "SL50", "sL60": "SL60", "sLMax": "SLMax", "track": "Track", "type": "Type", "unit": "Unit", "vHRMax": "VHRMax", "vMax": "VMax", "work": "Work"}}, "worksheet": {"addFarrierDentist": "Add <PERSON>/Dentist", "addProcedure": "Add Procedure", "assignedTo": "Assigned To", "barnPaddock": "Barn/Paddock", "box": "Box", "brands": "Brands", "comment": "Comment", "completeFarrierDentist": "Complete Far<PERSON>/Dentist", "completeProcedure": "Complete Procedure", "date": "Date", "deleteFarrierDentist": "Delete Farrier/Dentist", "deleteProcedure": "Delete Procedure", "description": "Description", "editFarrierDentist": "<PERSON>/Dentist", "editProcedure": "Edit Procedure", "horseName": "Horse Name", "horseType": "Horse Type", "location": "Location", "notes": "Notes", "setReminder": "<PERSON>minder", "task": "Task(s)"}}, "staff": {"activate": {"deactivate": "Deactivate", "deactivateConfirmMessage": "Are you sure to deactivate this staff account?", "deactivateHasPendingTaskMessage": "This staff member still has some outstanding tasks that are yet to be completed. Would you like to mark them all as completed or do you wish to manually re-assign them?", "manuallyReassign": "Manually Re-assign", "maskAsCompleted": "<PERSON> as Completed", "reactivate": "Reactivate ", "reactivateConfirmMessage": "Are you sure want to reactivate this account?", "removeConfirmMessage": "Are you sure you want to remove this staff?"}, "addStaff": "Add Staff", "comingSoon": "Coming soon", "create": {"abn": "ABN", "addPositionSuccess": "Add positions successfully", "address": "Address", "companyName": "Company Name", "country": "Country", "dateOfBirth": "Date of Birth", "debtor": "Debtor #", "displayName": "Display Name", "email": "Email", "firstName": "First Name", "gst": "GST", "lastName": "Last Name", "mobileNumber": "Mobile Number", "permissions": "Permissions", "position": "Position", "postcode": "Postcode", "state": "State", "suburb": "Suburb"}, "crm": {"assignedTo": "Assigned To", "comment": "Comment", "description": "Description", "ownerName": "Owner Name", "setReminder": "<PERSON>minder", "status": "Status", "time": "Time", "trainer": "Trainer"}, "currentlyNoStaff": "There is currently no staff", "editStaff": "Edit Staff", "filter": {"allPositions": "All Positions", "allStatus": "All Status", "selectPosition": "Position", "selectStatus": "Status", "statuses": {"active": "Active", "awaitingApproval": "Awaiting approval", "awaitingStaff": "Awaiting Staff", "inactive": "Inactive"}}, "general": {"category": "Category", "common": {"action": "Action", "assignedTo": "Assigned To", "category": "Category", "comment": "Comment", "date": "Date", "description": "Description", "empty": "There is currently no Task scheduled for this Horse", "status": "Status"}, "inputComment": "Input Comment", "modal": {"confirmDeleteTask": "Are you sure you want to delete this task?", "confirmDeleteTaskComplete": "You are about to delete a task that may impact your transactions and owner billings. Are you sure you want to continue?"}, "notification": {"deleteStaffTaskError": "General task error", "deleteStaffTaskSuccess": "General task successfully deleted"}, "taskSchedule": "Task Schedule", "tooltip": {"setReminder": "<PERSON>minder"}}, "headerNav": {"crmTask": "CRM Task", "general": "General", "procedure": "Procedure", "profile": "Profile", "transport": "Transport", "work": "Work"}, "info": {"lastLogin": "Last login"}, "notFoundContent": "You currently have no Staff or suppliers in your Staff list. To better manage your Stable and assign tasks and roles, be sure to add Staff and update their permissions.", "notFoundTitle": "There is currently no staff", "pendingTasks": "This staff still has some pending tasks. Would you like to change the position of this staff?", "placeholder": {"abn": "Input ABN", "address": "Input Address", "companyName": "Input Company Name", "country": "Select Country", "dateOfBirth": "DOB", "debtor": "Input Debtor", "displayName": "Input Display Name", "email": "Input Email", "firstName": "Input First Name", "lastName": "Input Last Name", "mobileNumber": "Input Mobile Number", "position": "Select Position", "postcode": "Input Postcode", "state": "Select State", "suburb": "Input Suburb"}, "profile": {"basicInfo": {"abn": "ABN", "address": "Address", "company": "Company", "dateOfBirth": "Date of Birth", "debtor": "Debtor #", "emailAddress": "Email Address", "fullname": "Full Name", "gst": "GST", "mobileNumber": "Mobile Number", "position": "Position", "sectionTitle": "Basic Info"}, "notFoundTitle": "Staff not found!"}, "remove": "Remove", "resend": "Resend Invitation", "resendConfirm": "Are you sure you want to resend invitation {name}?", "searchStaff": "Search Staff"}, "subscriptions": {"account": "Account", "billingAddress": "Billing Address", "breeding": "Breeding", "changePlanWarning": "To change your plan for the Prism subscription, please contact our Sales team. Would you like to proceed?", "comms": "Communications", "company": "Company", "contactEmail": "Contact mail", "crm": "CRM", "finance": "Finance", "fullName": "Full Name", "management": "Management", "monthlyFee": "Monthly Fee", "paymentMethod": "Payment Method", "phone": "Phone", "plus": "Plus", "sendEmail": "Send Email", "standardWebsite": "Standard Website", "subscription": "Subscription", "total": "Total", "wrkplaceHr": "Wrkplace HR"}, "supplier": {"UnarchiveSupplier": "Unarchive Supplier", "addSupplier": "Add Supplier", "archiveSupplier": "Archive Supplier", "basicInfo": "Basic Information", "billDueDate": "day(s) after the bill date", "currentlyNoSupplier": "There is currently no supplier", "editSupplier": "Edit Supplier", "message": {"archiveMessage": "Are you sure you want to archive it?", "archiveSuccess": "Archive a supplier successfully", "confirmCloseModal": "Are you sure you want to cancel? All changes will be lost", "createdSuccess": "Supplier successfully created", "idAddedTo": "This Prism ID has been added to {name}.", "invalidId": "This Prism ID does not exist in the system.", "invalidMobile": "Input invalid data into Mobile number field", "requireField": "This field is required", "unarchiveMessage": "Are you sure you want to move this supplier to active list?", "unarchiveSuccess": "Supplier has been unarchived", "updatedSuccess": "Supplier successfully updated"}, "modalSupplier": {"Abn": "Input ABN", "Debtor": "Input debtor", "address": "Input Address", "archive": "Archive", "bankAccountName": "Bank Account Name", "bankAccountNumber": "xxxxxx-xxxxxxxxxx", "billDue": "<PERSON>", "cancel": "Cancel", "country": "Input Country", "defaultAccount": "De<PERSON><PERSON> Account", "emailAddress": "Input Email Address", "firstName": "Input First Name", "gstRegister": "GST Register", "lastName": "Input Last Name", "mobileNumber": "Input Mobile Number", "noTax": "No Tax", "postalCode": "Postal Code", "primaryUser": "Input Contact Name", "prismId": "Input Prism ID", "save": "Save", "state": "State", "suburb": "Input Suburb", "supplierName": "Input Supplier Name", "tax": "Default purchase GST", "taxExclusive": "Tax Exclusive", "taxInclusive": "Tax Inclusive", "unarchive": "Unarchive"}, "notFound": "You currently have no suppliers in your Supplier list.", "notFoundContent": "You currently have no suppliers in your Supplier list.", "notFoundTitle": "There is currently no supplier", "profile": {"Abn": "ABN", "Address": "Address", "Debtor": "Debtor #", "bankAccountName": "Bank Account Name", "bankAccountNumber": "BSB & Bank Account Number", "contactName": "Contact Name", "country": "Country", "dueDate": "Due Date", "emailAddress": "Email Address", "enableSharing": "Enable sharing of horse, owners and horse ownership to this supplier", "firstName": "First Name", "gstRegister": "GST Register", "invoiceDueDate": "Invoice Due Date", "lastName": "Last Name", "mobileNumber": "Mobile Number", "postalCode": "Postal Code", "primaryUser": "Primary User", "prismID": "Prism ID", "purchaseSetting": "Purchase Setting", "sendRequest": "Send Request", "sendRequestLabel": "To allow this customer to import invoices into their Prism portal, enter the customer's Prism ID or click Send Request", "state": "State", "suburb": "Suburb", "supplierName": "Supplier Name", "tax": "TAX"}, "sendRequestErrorNotification": "Sent email failed. Please try again.", "sendRequestSuccessNotification": "Sent email successfully.", "supplierStatus": {"archived": "Archived", "current": "Current"}}, "syndicator": {"address": "Address", "country": "Country", "currentShare": "Current Share", "emailAddress": "Email Address", "fullName": "Full Name", "horse": "Horse", "horses": "Horses", "lastPurchased": "Last Purchased", "lastSold": "Last Sold", "mobileNumber": "Mobile Number", "noData": "There is currently no Horse.", "noDatadescription": "It looks like there are no Syndicators in your List.", "noDatatitle": "There is currently no Syndicator", "placeholderInput": "Search Syndicator", "postCode": "Post Code", "sectionTitle": "Basic Info", "state": "State", "suburb": "Suburb", "tab": {"label": {"invoice": "Invoice", "profile": "Profile"}, "profile": {"action": {"paynow": "Pay Now"}}}}, "task": {"common": {"action": "Action", "adviseOthers": "Advise Others", "assignedTo": "Assigned to", "barnBox": "Barn-Box", "clearDayProcedureAlert": "Clear Day Procedure Alert", "comment": "Comment", "confirmMsgCompletedTask": "Please specify task completion time", "confirmMsgCompletedTaskForAllHorse": "Please specify task completion time for all horses", "confirmMsgCompletedTaskForSingleHorse": "Please specify task completion time for this horse", "confirmMsgCompletedTaskForTheFuture": "You are confirming the completion time of a task in the future", "confirmMsgTaskCompletionInFuture": "You are confirming the completion of a task set for the future. Do you wish to continue?", "date": "Date", "dateInFuture": "Some due dates are in the future. Do you want to complete this task at current time?", "defaultRider": "De<PERSON><PERSON> Rider", "description": "Description", "dueDate": "Due Date", "dueDateOfTask": "Due date of task", "enterNameOrEmail": "Input Name or Email", "filterByLocation": "Filter by Location", "filterByStatus": "Filter by Status", "gear": "Gear", "horse": "Horse", "horseName": "Horse Name", "inputComment": "Input Comment", "linkedTo": "Linked to", "location": "Location", "markCompleted": "Mark as completed", "markCompletedSuccess": "Task has been marked as completed successfully", "moreActions": "More actions", "other": "Other", "ownerName": "Owner Name", "procedure": "Procedure", "racingAlert": "Racing within Clear Day Guidelines", "reason": "Reason", "repeat": "Repeat", "selectDriver": "Select Driver", "selectGear": "Select Gear", "selectJockey": "Select Jockey", "selectReason": "Select Reason", "selectStaff": "Select Staff", "selectStatus": "Select Status", "selectSupplier": "Select Supplier", "selectTack": "Select Tack", "setAsDefaultGear": "Set as default Work Gear for this horse", "setAsDefaultRider": "Set as default Work Rider for this horse", "setAsDefaultTack": "Set as default Tack for this horse", "specificDate": "Specific Date", "specifyCompletionTime": "Please specify task completion time", "staff": "Staff", "status": "Status", "supplier": "Supplier", "tack": "Tack", "taskCompletion": "Task Completion", "time": "Time", "trainerName": "Trainer Name", "transport": "Transport", "unassigned": "Unassigned", "work": "Work"}, "procedure": {"accepted": "accepted", "accountPay": "Account (Pay)", "accountRec": "Account (Rec)", "addNewProcedure": "Add New Procedure", "addedToVaccinationHistory": "Added to Vaccination History", "alertInjection": "Australian Rule of Racing AR.178AB prohibits the use of injections of any type in horses during the One Clear Day prior to racing and official trials.", "allProcedures": "All procedures", "allSuppliers": "All Suppliers", "allTask": "All procedures", "amount": "Amount", "chooseExistingPreset": "Choose From Existing Procedure Preset", "clearDayAlert": "This is a guide only and you are responsible for presenting your horse drug free on race day as per the Australian Rules of Racing. Note also that multiple doses and method of administration can affect clear days. Be sure to consult your Vet or Authorities if in doubt and as required.", "clearDayInfo": "The general recommended clear days for {specificName} is {clearDay} days.", "clickTheClock": "Click the Clock to Add new Time", "cloneThisTask": "Clone this task to a future date", "confirmDeleteProcedure": " Are you sure you want to delete this Procedure?", "confirmDeleteProcedureWithFinance": "You are about to delete a task that may impact your transactions and owner billings. Are you sure you want to continue?", "confirmDeleteRecurringProcedure": "Would you like to delete only this procedure or all recurring procedures in this series?", "cost": "Cost", "createProcedureSchedule": "Create Procedure Schedule", "createProcedureSuccess": "Procedure has been added successfully", "deleteRecurringProcedure": "Delete Recurring Procedure", "deleteRuleConfirm": "Are you sure you want to delete this Procedure charge?", "description": "Description", "editProcedureSchedule": "Edit Procedure Schedule", "editRecurringProcedure": "Edit Recurring Procedure", "editRecurringProcedureConfirm": "Would you like to change only this procedure or all recurring procedures in this series?", "enterAllRequiredInformation": "Please enter all required information before saving as a Preset.", "extra": "Extra", "frequency": "Frequency", "horseAcceptedForRace": "{horseName} has {acceptText} for race at {raceLocation} on {raceDate}.", "invoiceMessage": "Invoice Message", "justThisTask": "Only this procedure", "method": "Method", "newProcedure": "New Procedure", "noDefinedRule": "There are currently no defined rules.", "noProcedureData": "There is currently no Procedure scheduled for this Horse", "noProcedurePresets": "You currently have no Procedure Preset", "nominated": "nominated", "onlyThisProcedure": "Only this procedure", "pleaseSelectAttribute": "Please select procedure Attribute", "presetProcedureDescription": "{hasSpecific<PERSON>abel, select, false {} other {{specificLabel}}}{hasSpecificClearDay, select, false {} other { ({specificClearDay}) }}{hasUnitOrAmount, select, false {} other { - {amount}{unit}}}{hasMethod, select, false {} other { - {method}}}", "procedure": "Procedure", "procedureDueDate": "Procedure Due Date", "procedureStartDate": "Procedure Start Date", "procedureStatus": "Procedure Status", "repeat": "Repeat", "repeatEvery": "Repeat every", "repeatMultiSummary": "Every {repeatEvery} {repeat, select, Daily {days} Weekly {weeks} Monthly {months} other {}}", "repeatOn": "Repeat on", "repeatOneSummary": "{repeat, select, Daily {Daily} Weekly {Weekly} Monthly {Monthly} other {}}", "repeatSettings": "Repeat Settings", "repeatTimeSummary": "{isShowRepeatDays, select, false {} other {on {repeatDays}}} at {time}{numberOfTimes, plural, =0 {} =1 {, # time} other {, # times}}, starting {startDate} {isShowEndDate, select, false {} other {and finishing {endDate}}}", "repeats": "Repeats", "sale": "Sale", "saveAsPreset": "Save as Preset", "selectCategory": "Select Category", "selectProcedureToSave": "Please select any procedure to Save as Preset", "selectSpecific": "Select Specific", "selectUnit": "Select Unit", "specific": "Specific", "successfullyDeletedConfigRule": "Finance Procedure Config successfully deleted", "successfullyDeletedPreset": "Procedure Preset successfully deleted", "successfullySavedPreset": "Successfully saved as a Preset", "thisProcedureAlreadyAdded": "This Procedure has already been added to the Preset list", "tooltipAllProcedures": "All other procedure events in the series will change", "tooltipOnlyThisProcedure": "All other procedure events in the series will remain the same", "total": "Total", "unit": "Unit", "vaccinationHistory": "Vaccination History", "warningNotSameSupplier": "The rules you have selected contain different suppliers, if you continue, the supplier information will be removed from the task."}, "setupBanner": {"emailSetting": "Set Up Email Banner & Footer", "enterYourFooterLink": "Enter your footer link", "enterYourHeaderLink": "Enter your header link", "footerBanner": "Footer banner", "footerText": "Footer Text", "upload": "Upload", "uploadBanner": "Upload Banner"}, "trackwork": {"addWorkPreset": "Add Work Preset", "chooseExistingPreset": "Choose from existing Work Preset", "confirmDeleteWork": "Are you sure you want to delete this Work?", "confirmMoveProceduresWithWork": "Do you wish to move attached Procedures across as well as Work?", "createWorkSchedule": "Create Work Schedule", "createWorkSuccessfully": "Work has been added successfully", "deleteWork": "Delete Work", "editWorkSchedule": "Edit Work Schedule", "enterWorkBeforeSave": "Please enter work value before saving as a Preset", "mustUnlinkHorseBeforeEdit": "You must unlink this Horse to edit this task", "newWork": "New Work", "noData": "There is currently no Work scheduled for this horse", "noWorkData": "There is currently no Work scheduled for this Horse", "postWork": "Post Work", "preWork": "Pre Work", "saveAsPreset": "Save as Preset", "selectOthers": "Select Others", "selectPostWork": "Select Post-Work", "selectPreWork": "Select Pre-Work", "selectWork": "Select Work", "setReminder": "<PERSON>minder", "successfullyDeletedPreset": "Work Preset successfully deleted", "successfullySavedPreset": "Successfully saved as a Preset", "thisWorkAlreadyAdded": "This work has already been added to the Preset list", "viewArrioneo": "Arrioneo info", "viewETrakka": "E-Trakka info", "work": "Work", "workEntryHasNoWorkDetail": "This Work entry has no actual work detailed. Do you wish to continue?"}, "transport": {"addNewTransport": "Add New Transport", "alsoConfirmCompletePendingSingleTask": "Do you also wish to confirm this Transport Task has been completed?", "amountAre": "Amount are", "chooseExistingPreset": "<PERSON>ose From Existing Preset", "chooseLocationStatusPreset": "Choose Location & Status Presets", "confirmCancelOriginal": "This task is a return trip. Do you want to also cancel the original task?", "confirmCancelReturnTrip": "This task has return trip. Do you want to also cancel it?", "confirmChangeLocationStatusCompleteTask": "By marking this transport task as completed on this date, you are changing the location/status history of <horse></horse> which will affect future billings.<br></br><br></br> To confirm the change to <horse></horse>'s location and status to <location></location> on <date></date> click Yes.", "confirmCompletePendingMultipleTask": "There are prior Transport Tasks for {horses, plural, =1 {this horse} other {these horses}} awaiting completion. Have these Transportations been completed?", "confirmCompletePendingSingleTask": "There is a prior Transport Task for {horses, plural, =1 {this horse} other {these horses}} awaiting completion - {dueDate} from {fromLocation} to {toLocation}. Has this Transportation been completed?", "confirmCompleteTaskInFuture": "You are confirming the completion of a task set for the future. Do you wish to continue?", "confirmDeleteTransport": "Are you sure you want to delete this Transport?", "confirmRemoveOriginTask": "This task is a return trip. Do you want to also remove the original task?", "confirmRemoveReturnTask": "This task has return trip. Do you want to also remove it?", "confirmRemoveReturnTrip": "Are you sure you want to remove return trip?", "confirmUpdateLocationStatus": "Do you want to update the location - status{numberOfHorses, plural, =1 {} other { for these horses}} and continue with the task?", "cost": "Cost", "createTransportSchedule": "Create Transport Schedule", "currentAmount": "Current Amount", "deleteRuleConfirm": "Are you sure you want to delete this Transport charge?", "dontAdjustLocationStatus": "Don't adjust Location/Status", "editTransportSchedule": "Edit Transport Schedule", "extra": "Extra", "financeConfigCreated": "Finance Transport Config successfully created", "fixedAmount": "Fixed Amount", "from": "From", "fromAndToMustNotBeTheSame": "The To and From location entries cannot be the same. Please select different locations", "fromLocation": "From Location", "horseNotInLocation": " {numberOfHorses, plural, =1 {is} other {are}} not currently located at ", "location": "Location", "locationStatusPreset": "LOCATION - STATUS PRESETS", "markCompletedSuccess": "The transport task has been completed", "markupLineItem": "Mark Up Line Item", "mustSelectLocation": "You should select a valid Location", "mustSelectLocationStatus": "You should select a valid Location-Status", "mustSelectStatus": "You should select a valid Status", "newTransport": "New Transport", "newTransportRule": "New Transport Rule", "noTransportData": "There is currently no Transport scheduled for this Horse", "originalTransportFrom": "Original Transport of Task on  ", "payableAccount": "Payable Account", "pleaseSelectFromAndToLocation": "Please select from location and to location.", "presetTransportDescription": "From {fromLocation} to {toLocation}", "purchaseDescription": "Purchase Description", "receivableAccount": "Receivable Account", "return": "Return", "returnTransportFrom": "Returning Transport from Task on ", "returningTransportFrom": "* Returning Transport from Task on {date}", "sale": "Sale", "salesDescription": "Sales Description", "selectTransportVehicle": "Select Transport Vehicle", "status": "Status", "successfullyDeletedConfigRule": "Finance Transport Config successfully deleted", "successfullyDeletedPreset": "Transport Preset successfully deleted", "taxRate": "Tax Rate", "to": "to", "toLocation": "To Location", "total": "Total", "trackingCategory": "Tracking Category", "transport": "Transport", "vehicle": "Vehicle"}}, "trainer": {"filter": {"statuses": {"approved": "Approved", "awaitingApproval": "Awaiting approval", "notRegistered": "Trainer not registered", "suspended": "Suspended", "unapproved": "Unapproved"}}, "headerNav": {"crmTask": "CRM Task", "general": "General", "invoice": "Invoice", "procedure": "Procedure", "profile": "Profile", "transport": "Transport", "work": "Work"}, "noDatatitle": "There is currently no Syndicator", "notFoundContent": "It looks like there are no Trainers in your List.<br></br> Be sure to request access and approval from the relevant Trainer and they will appear here.", "notFoundTitle": "There is currently no Trainers.", "profile": {"basicInfo": {"abn": "ABN", "address": "Address", "company": "Company", "country": "Country", "emailAddress": "Email Address", "gst": "GST", "location": "Location", "mobileNumber": "Mobile Number", "postcode": "Post code", "sectionTitle": "Basic Info", "state": "State", "statusFilter": {"allTrainers": "All Trainers", "awaitingApproval": "Awaiting approval", "registeredOnly": "Registered Only", "unregisteredOnly": "Unregistered Only"}, "suburb": "Suburb"}, "headerTable": {"currentShare": "Current Share", "firstPurchased": "First Purchased", "horse": "Horse", "lastPurchased": "Last Purchased", "lastSold": "Last Sold", "noHorseData": "There is currently no Horse.", "status": "Status"}, "tableArchiveHorse": "Archived Horse", "tableHorseList": "Horse List"}, "search": {"placeholderInput": "Search Trainer"}}, "user": {"bulkUpdate": {"adjustmentDate": "Adjustment Date", "barnPaddock": "Barn/Paddock", "barnPaddockBox": "Barn/Paddock-Box", "barnPaddockUpdate": "Barn/Paddock Update", "bookedTo": "Booked To", "box": "Box", "category": "Category", "confirmChangeFilter": "If you change condition, the change you have made will be reset. Are you sure you want to continue?", "dam": "Dam", "due": "Due", "foaled": "Foaled", "horseBarnPaddockUpdate": "Horse Barn/Paddock Update", "horseCategory": "Horse Category", "horseLocationUpdate": "Horse Location/Status Update", "horseName": "Horse Name", "horseType": "Horse Type", "includeArchivedHorse": "Include Archived Horse", "lastRun": "Last Run", "lastStatus": "Last Status", "location": "Location", "newBarnPaddock": "New Barn/Paddock", "newBox": "New Box", "newCategory": "New Category", "newLocation": "New Location", "newStatus": "New Status", "newType": "New Type", "ok": "OK", "onlyMareFoaled": "Only <PERSON> Foaled", "selectBarnPaddock": "Select Paddock", "selectBox": "Select Box", "selectHorseCategory": "Select Horse Category", "selectHorseType": "Select Horse Type", "selectLocation": "Select Location", "selectOption": "Select Option", "selectStatus": "Select Status", "selectUpdateType": "Select Update Type", "sire": "<PERSON>e", "status": "Status", "type": "Type", "updateType": "Update Type"}, "modals": {"masterConfig": {"action": {"addBarnFailed": "Create barn failed", "addBarnSuccess": "<PERSON><PERSON> successfully created", "addBoxFailed": "Create box failed", "addBoxSuccess": "Box successfully created", "addFailed": "Failed to add config.", "addHorseStatusSuccess": "Horse Status successfully created", "addItem": "Add item", "addSuccess": "Config has been added successfully.", "clearDay": "Clear day", "deleteConfirm": "Are you sure to delete this?", "deleteFailed": "Failed to delete config.", "deleteLabel": "Delete Confirm", "deleteSuccess": "Config has been deleted successfully.", "existError": "This {type} already exists in list", "isExistingInGroup": "{owners} is existing in group.", "orderFailed": "Failed to re-order config.", "orderSuccess": "Config has been re-ordered successfully.", "printAllBarnsConfirm": "Are you sure you want to print all barns ?", "printAllBoxesConfirm": "Are you sure you want to print all boxes ?", "scanBarnFailed": "Failed to scan barn", "scanBoxFailed": "Failed to scan box", "searchPlaceholder": "Type and hit Enter to search", "showAtTop": "Click the button to show at the top", "sortConfirm": "Are you sure you wish to sort this list alphabetically?", "sortFailed": "Failed to sort configs", "sortSuccess": "Sort configs successfully.", "updateFailed": "Failed to update config.", "updateSuccess": "Config has been updated successfully."}, "feedAndSupplements": {"feed": "Feed", "feedTimes": "Feed Times", "supplements": "Supplements"}, "generalType": {"dentist": "Dentist", "farrier": "<PERSON><PERSON>"}, "horseNote": {"bloodProfile": "Blood Profile", "scope": "<PERSON><PERSON>"}, "label": {"add": "Add", "addNew": "Add New", "addNewBarn": "Add New Barn", "addNewBox": "Add New Box", "addNewGroup": "Add New Group", "barnName": "Barn Name", "boxName": "Box Name", "category": "Category", "editBarnName": "<PERSON> <PERSON><PERSON>", "editBoxName": "Edit Box Name", "editPreset": "Edit Preset", "groupName": "Group Name", "label": "Label", "location": "Location", "moveCategory": "Move to other category", "noBarnSelected": "Please select a barn to view box list", "noData": "No data", "printBarnQRCode": "Print Barn QR code", "printBoxQRCode": "Print Box QR code", "procedureCategory": "Procedure category", "screen": "Screen", "selectCategory": "Select Category", "selectLocation": "Select Location", "selectOwner": "Select owner", "selectOwners": "Select Owners", "selectStatus": "Select Status", "sortAZ": "Click to sort A->Z", "sortZA": "Click to sort Z->A", "status": "Status", "vaccine": "Vaccine", "value": "Value"}, "locationAndStatus": {"from": "From", "location": "Location", "presets": "Location & Status Presets", "status": "Status", "updateLocation": "Update Location", "updateLocationDesc": "These horses will be affected by your change. Please be sure all horses are updated before continue.", "updateStatus": "Update Status"}, "procedure": {"category": "Category", "specific": "Specific", "treatment": "Treatment"}, "screen": {"barnBox": "Barn/Box", "feed&supplements": "Feed & Supplements", "generalType": "General Type", "horseNote": "Horse Note", "locationAndStatus": "Horse Location & Status", "ownerGroup": "Owner Group", "procedure": "Procedure", "transportVehicle": "Transport - Vehicle", "unitsOfAmount": "Units of amount", "work": "Work"}, "work": {"gear": "Gear", "group": "Group", "other": "Other", "postWork": "Post-work", "preWork": "Pre-work", "tack": "Tack", "work": "Work"}}, "organizationConfig": {"buttons": {"cancel": "Cancel", "preview": "Preview", "save": "Save"}, "form": {"fields": {"abn": {"label": "{value}", "placeholder": "Input {value}"}, "address": {"label": "Street", "placeholder": "Input street"}, "attachments": {"label": "Attachments", "tooltip": "Attach any forms or items that you wish to send out with your Invoices (eg., as Terms and Conditions)"}, "charityAccount": {"label": "Charity Donation", "placeholder": "Select account"}, "charityTaxRate": {"placeholder": "Select tax rate"}, "company": {"label": "Company", "placeholder": "Input company"}, "content": {"label": "Content"}, "country": {"label": "Country", "placeholder": "Select country"}, "currency": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "decimalCharFormat": {"label": "Decimal Format", "placeholder": "Input decimal format"}, "directDebitMessage": {"label": "Direct Debit Process", "placeholder": "Input message"}, "dueDateOption": {"placeholder": "Select"}, "email": {"label": "Email", "placeholder": "Input email"}, "fax": {"label": "Fax", "placeholder": "Input fax"}, "gstRegistered": {"label": "GST Registration", "placeholder": "Select", "tooltip": "You are currently set up in Prism and Xero as {registered, select, true {registered} false {not registered} other {not registered}} for GST. To update this <NAME_EMAIL>"}, "interestAccount": {"label": "Interest Account", "placeholder": "Select interest account"}, "interestPeriod": {"placeholder": "day(s) overdue"}, "interestRate": {"placeholder": "Input rate"}, "interestSettings": {"label": "Interest Settings"}, "interestTaxRate": {"placeholder": "Select tax rate"}, "invoiceOrder": {"label": "Group by category", "tooltip": "Switching this ON will display transactions in Invoices in Category order (Daily Charges, Procedures, Transport, Farrier, Dentist and Others); OFF will display in chronological order."}, "mobile": {"label": "Mobile", "placeholder": "Input mobile number"}, "overdueBox": {"label": "Show Overdue in the statements"}, "overduePeriod": {"label": "Invoice due date", "placeholder": "Invoice due"}, "phone": {"label": "Phone", "placeholder": "Input phone number"}, "postcode": {"label": "Post Code/Zip", "placeholder": "Input post code/zip"}, "prismId": {"label": "Prism ID", "placeholder": "Input Prism ID"}, "region": {"label": "Region", "placeholder": "Select region"}, "sendEmail": {"label": "{enabled, select, true {Send} false {Don't send} other {Send}} email to owner", "tooltip": "Switching this off will ensure you don't send Statements to your Owners during your trial period. If you switch this on, you will be able to send your statements to your Owners."}, "showDirectDebitLink": {"label": "Include direct debit authorisation link"}, "showPaymentNote": {"label": "Show payment note for direct debit customers", "tooltip": "Switching this ON will display the Payment Note on statements for owners who have authorised direct debit. Leaving this OFF will hide the Payment Note for direct debit owners."}, "stallion": {"label": "Stallion"}, "state": {"label": "State", "placeholder": "Select state"}, "statementCodeExample": {"label": "Statement ID example"}, "statementCodeFormat": {"label": "Statement ID format", "placeholder": "Input format"}, "statementPrefix": {"label": "Prefix", "placeholder": "Input prefix"}, "subject": {"label": "Subject", "placeholder": "Input subject"}, "suburb": {"label": "Suburb/Town", "placeholder": "Input suburb/town"}, "syncFreeInvoice": {"label": "Sync fully discounted ($0) invoices"}, "syncXero": {"label": "Sync Credit Note to Xero", "placeholder": "{enabled, select, true {After creating Credit Note} false {After generating Statement} other { }}"}, "taxRate": {"label": "Tax", "placeholder": "Input tax rate"}, "website": {"label": "Website", "placeholder": "Input website"}}}, "tooltips": {"clickToUploadLogo": "Click to upload logo", "directDebitEmail": "This is the default text that will appear in your Direct Debit Emails.", "invoiceEmail": "This is the default text that will appear in the body of your Invoice Emails.", "paymentNote": "This is the text that will appear at the bottom of your Invoices and can be used to let your Owners know about payment terms, EFT transfer details and any other items you need to advise.", "statementEmail": "This is the default text that will appear in the body of your Statement Emails.", "statementReminderEmail": "This is the default text that will appear in your Overdue Reminder Emails."}}}, "nav": {"emailSetting": "Set Up Email Banner & Footer", "organisationConfig": "Organisation Config", "paymentSetting": "Payment Setting"}, "profile": {"Address": "Address", "DOB": "DOB", "abn": "ABN", "addEmail": "Add <PERSON>", "addNew": "Add new", "checkEmailToConfirm": "Requires Validation - check email to confirm", "company": "Company", "confirmDeleteEmail": "Are you sure you want to delete this email?", "country": "Country", "dateOfBirth": "Date of Birth", "deleteEmail": "Delete Email", "editEmail": "<PERSON> Email", "editProfile": "Edit profile", "emailDisplayName": "Email Display Name", "emailList": "Email List", "enterABn": "Input ABN", "enterAddress": "Input address", "enterCompany": "Input company", "enterFirstName": "Input First Name", "enterGst": "Input GST", "enterLastName": "Input Last Name", "enterMobileNumber": "Mobile Number", "enterName": "Input Name", "enterPostcode": "Input postcode", "enterSuburb": "Input Suburb", "firstName": "First Name", "fullName": "Full Name", "gst": "GST", "lastName": "Last Name", "location": "Location", "mobile": "Mobile", "notice": "Notice", "postcode": "Postcode", "primaryEmail": "Primary Email", "prismID": "Prism ID", "region": "Region", "removeEmail": "Remove this email address", "selectCountry": "Select Country", "selectLocation": "Select Location", "selectRegion": "Select Region", "selectState": "Select State", "setPrimaryEmail": "Set this to be Primary Email", "stableName": "Stable Name", "state": "State", "suburb": "Suburb", "updateYourEmail": "You have updated your email address.\nPlease check your email and verify it to complete the update.\nThe verification link will expire in 72 hours.", "username": "Username", "validEmail": "Please enter a valid email address", "verificationEmail": "This email requires verification before it can be made the primary email. Please save your changes and then click on the link to verify and then make it your primary email."}}, "utilities": {"nav": {"barnPaddockUpdate": "Barn/Paddock Update", "categoryUpdate": "Category Update", "horseBarnPaddockUpdate": "Horse Barn/Paddock Update", "horseCategoryUpdate": "Horse Category Update", "horseLocationUpdate": "Location/Status Update", "horseTypeUpdate": "Horse Type Update", "locationUpdate": "Horse Location/Status Update", "praForms": "PRA Forms"}, "runSuccessfully": "Run successfully", "selectOne": "Please select at least one item to update"}}, "messages": {"api": {"serviceUnavailable": "Service is currently unavailable. Please try again later.", "unknownError": "Something went wrong. Please try again later."}, "comms": {"groupUpdate": {"discardSuccess": "Group Update has been discarded successfully", "missingReportDescription": "Please note there are some missing updates in the horse's selected. Please check the horses you are updating and adjust as required.", "notFoundDescription": "You have not published any updates yet. Use Group Update template to keep Owners updated and informed of News in your stable.", "notFoundDescriptionOwner": "There are currently no Group Updates from any of your Trainers", "ownership": "<bold>{horses}</bold> currently {count, plural, =0 {doesn't} =1 {doesn't} other {don't}} have any owners. Do you really want to continue?", "publishSuccess": "Group Update successfully published", "saveAsDraftSuccess": "Group Update successfully saved as draft", "sendEmailSuccess": "Group Update sent to emails successfully", "thisHorse": "This horse"}, "invalidEmail": "Invalid email address. Please try again.", "notFoundTitle": "No updates found.", "stableUpdate": {"notFoundDescription": "You have not published any updates yet. Use Stable Update template to keep Owners updated and informed of News in your stable.", "notFoundDescriptionOwner": "There are currently no Stable Updates from any of your Trainers", "sendEmailSuccess": "Stable Update sent to emails successfully"}}, "errors": {"common": {"genericError": "An unexpected error has occurred. Please try again later.", "somethingWentWrong": "Something went wrong"}}, "finance": {"canNotGenerateStatement": "Can not generate statement preview.", "directDebit": {"closeSuccess": "The owner is marked as closed successfully", "send": {"confirm": "Are you sure you want to send Direct Debit Request to owner?"}}, "rules": {"confirmDeleteDayRate": "Are you sure you want to delete this Day Rate charge?", "confirmEditDayRate": "Are you sure you want to update this rule? This may affect the current expenses.", "locationStatusNA": "Sorry. You cannot add day rate for N/A/ N/A.", "notFoundDayRateDescription": "There are currently no defined day rates.", "notFoundDescription": "There are currently no defined {value} rules.", "notFoundManagementFeeRule": "There are currently no defined management fee rules", "notFoundTitle": "No {value} found"}, "transaction": {"confirmXero": "This Xero authorisation and sync will merge your Tax Rates, Chart of Account, Suppliers, Owners, Bills, Expenses and Invoices.<br></br><br></br> Click OK to proceed and follow the prompt to authorise Prism to access your Xero account and manage the synchronisation process.", "continueWithoutEmailingStatements": "You are about to continue without emailing statements, are you sure?", "xeroAuthorisation": "Xero authorisation"}}, "horse": {"emailOwner": "Each report will automatically be sent to the Owners of this Horse who are registered and opted in to receive updates. If you wish to email others this report, please input their email to the box below.", "empty": {"default": "There are currently no Horses added to your Stable. You can add more horses by clicking on the Add Horse button <NAME_EMAIL> if you are looking to import a large number of horses at once.", "owner": "<p>There are currently no Horses in your Horse list.</p><p>For Horses that you own, be sure to request access from your Trainer or use the Add Horse button below to send them an access request.</p><p>Note that this Horse list is ONLY for horses that you have a stake in.</p><p>It is not a Blackbook, so please do not request access for horses other than your own.</p>", "syndicator": "<p>There are currently no Horses in your Horse list.</p><p>For Horses that you own, be sure to request access from your Trainer or use the Add Horse button below to send them an access request.</p><p>Note that this Horse list is ONLY for horses that you have a stake in.</p><p>It is not a Blackbook, so please do not request access for horses other than your own.</p>"}, "media": {"forward": {"forwardSuccess": "Shared successfully", "invalidEmails": "Invalid email {count, plural, =0 {address} =1 {address} other {addresses}}: <bold>{emails}</bold>. Please try again!"}, "noAlbumsToShow": "There is currently no albums to show.", "notFoundDescription": "There are currently no {value} to show for this horse.<br></br> Be sure to enhance your Owners experience by adding some Media to the Horses profile.", "notFoundTitle": "No {value} found", "notFoundTitleTrainer": "There are currently no {value} to show for this <PERSON>.", "publish": {"noDraftMedia": "There are currently no draft Media available for this Horse", "noMediaSelected": "No media selected. Please select media to publish.", "publishSuccess": "{count, plural, =1 {Media} other {Medias}} successfully published"}}, "noProcedure": "There is currently no Procedure scheduled for this Horse", "noTask": "There is currently no Task scheduled", "sentEmailSuccess": "Your emails are being processed and will be completed in a few minutes.", "work": {"alreadyHasWork": "You have already added Work for {horses} on date {dates}. Do you wish to continue?"}}, "media": {"invalidFileExtension": "Invalid file extension!"}, "schedule": {"general": {"cantCopy": "There {totalUnauthorized, plural, =1 {is # task} other {are # tasks}} of {trainersList} that can't be copied because you don't have permission.", "copied": "`You have copied {successfulTasksCount, plural, =1 {task} other {tasks}} successfully<br>`"}, "note": {"confirmDelete": "Are you sure you want to delete this Note?"}, "work": {"confirmDelete": "Are you sure you want to delete {count, plural, =1 {this task} other {these tasks}}?", "confirmDetach": "Are you sure you want to detach?", "confirmMoveBetweenGroups": "You sure you want to put {horses} from {sourceGroup} to {targetGroup}?", "confirmMoveIntoGroup": "You sure you want to put {horses} into {group}?", "confirmMoveOutOfGroup": "You sure you want to put {horses} out of {group}?", "copy": {"existedWarning": "You have already added Work for {horses} on this date. Do you wish to continue?", "success": "{count, plural, =1 {Task} other {Tasks}} successfully copied"}, "create": {"alreadyHasWork": "You have already added Work for {horses} on this date. Do you wish to continue?", "noWork": "This Work entry has no actual work detailed. Do you wish to continue?"}, "deleteMultipleSuccess": "You have deleted {count} tasks successfully", "hasUpdates": "This list has new updates", "link": {"alreadyLinkedWithOtherHorses": "Horses selected are separately linked with other horses. Would you like to link all horses together? If not, please cancel and unlink horses first.", "linked": "Horses now linked", "sameWork": "You are linking horses who are not doing the same work. Are you sure you want to continue?", "taskHasNoWork": "You can only link horses that have work", "unlinked": "Horses now unlinked"}, "tableNoData": "There is currently no Work scheduled for {date}", "ungroupSuccess": "{horses, plural, =1 {Horse} other {Horses}} now ungrouped", "workListChanged": "Someone has made new changes on Work list. Please reload Work list before continuing"}}, "statusObject": {"awaitingApproval": "Awaiting approval", "awaitingLogin": "Awaiting <PERSON><PERSON>", "deactivated": "Deactivated", "inviteTrainer": "<PERSON><PERSON><PERSON>", "mediaNotUpdateIn3months": "This owner is yet  to have a report or media update for 3 months", "mediaNotUpdateInNmonth": "This owner has not had any updates for {month} days", "neverLogin": "Never logged in", "suspended": "Suspended", "trainerNotRegistered": "Trainer not Registered", "unregisteredOwner": "Unregistered Owner", "waitingStaffConfirm": "Waiting for staff confirmation"}, "unsavedChanges": {"createStatement": "Are you sure you want to close? All changes will be saved, however, you will need to start the Create Statement process again.", "message": "Are you sure you want to cancel? All data will be lost.", "title": "Unsaved data"}, "validation": {"maxLength": "You have entered more than the maximum {amount} characters", "optionAlreadyExist": "This data already existed.", "required": "This field is required"}}}