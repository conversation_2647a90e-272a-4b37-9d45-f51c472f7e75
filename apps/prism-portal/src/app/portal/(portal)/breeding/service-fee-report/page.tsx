import { dehydrate, HydrationBoundary, QueryClient } from '@tanstack/react-query';
import { isNil, omitBy, orderBy } from 'lodash';
import { redirect } from 'next/navigation';

import { getBreedingServiceFeeReport, getBreedingServiceFeeReportFilter, getTrainerList } from '@/api';
import { auth } from '@/api/auth';
import { BreedingServiceFeeReport } from '@/components/breeding/breeding-service-fee-report';
import { defaultListRequest, getAccountRole, PAGE_SIZE_MD, QueryKey } from '@/core';

const BreedingServiceFeeReportPage = async () => {
	const queryClient = new QueryClient();
	const session = await auth();

	if (!session) redirect('/portal/login');

	const isStaff = getAccountRole(session).is('Staff');

	const res = await getTrainerList({ status: null }, isStaff);
	const trainer = res?.responseData ?? [];
	const trainerId = session.user?.trainerId ?? trainer[0]?.id;

	const resFilter = await getBreedingServiceFeeReportFilter({ trainerId });

	queryClient.setQueryData([QueryKey.BreedingServiceFeeReportFilter, { trainerId }], resFilter);

	const seasons = resFilter?.responseData?.seasons;

	if (Array.isArray(seasons) && seasons.length > 0) {
		const seasonList = orderBy(
			seasons.map((s: string) => parseInt(s, 10)),
			[],
			['desc']
		);

		const payload = {
			trainerId,
			seasons: seasonList[0],
			orderBy: 'season',
			orderDirection: 'desc',
		};
		const sanitizedPayload = omitBy(payload, isNil);

		await queryClient.prefetchInfiniteQuery({
			queryKey: [QueryKey.BreedingServiceFeeReport, payload],
			queryFn: ({ pageParam }) =>
				getBreedingServiceFeeReport({
					...pageParam,
					...sanitizedPayload,
				}),
			initialPageParam: { ...defaultListRequest, pageSize: PAGE_SIZE_MD },
		});
	}

	return (
		<HydrationBoundary state={dehydrate(queryClient)}>
			<BreedingServiceFeeReport />
		</HydrationBoundary>
	);
};

export default BreedingServiceFeeReportPage;
