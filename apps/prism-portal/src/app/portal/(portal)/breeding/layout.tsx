import type React from 'react';
import { Box } from '@mantine/core';
import { type Metadata } from 'next';

import { BreedingHeader } from '@/components';
import { BreedingProvider } from '@/core';

export const metadata: Metadata = {
	title: 'Breeding',
};

interface BreedingLayoutProps {
	children: React.ReactNode;
}

const BreedingLayout: React.FC<BreedingLayoutProps> = ({ children }) => {
	return (
		<div className='relative flex h-full flex-1 flex-col overflow-hidden'>
			<BreedingProvider serviceFeeReportState={{ filters: { orderBy: 'season', orderDirection: 'desc' } }}>
				<BreedingHeader />
				<Box className='flex min-h-0 flex-1 justify-center p-4 pb-0'>{children}</Box>
			</BreedingProvider>
		</div>
	);
};

export default BreedingLayout;
