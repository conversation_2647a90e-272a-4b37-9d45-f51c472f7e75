'use client';

import { createContext, use, useMemo, useState } from 'react';
import type { MRT_RowSelectionState } from 'mantine-react-table';

import type { Breeding, BreedingServiceFeeReportPayload } from '@/core';

interface ServiceFreeReportState {
	data?: {
		totalInvoice: number;
		totalPaid: number;
		totalCredit: number;
		totalOutstanding: number;
	};
	filters?: BreedingServiceFeeReportPayload;
}

interface Total {
	serviceFeeReport: number;
}

interface BreedingContextProps {
	children: React.ReactNode;
	rowSelection?: MRT_RowSelectionState;
	list?: Breeding[];
	setRowSelection?: React.Dispatch<React.SetStateAction<MRT_RowSelectionState>>;
	setList?: React.Dispatch<React.SetStateAction<Breeding[]>>;
	serviceFeeReportState?: ServiceFreeReportState;
}

export interface BreedingContextValue {
	children: React.ReactNode;
	rowSelection?: MRT_RowSelectionState;
	list?: Breeding[];
	setRowSelection?: React.Dispatch<React.SetStateAction<MRT_RowSelectionState>>;
	setList?: React.Dispatch<React.SetStateAction<Breeding[]>>;
	serviceFeeReportState: ServiceFreeReportState;
	setServiceFeeReportState: React.Dispatch<React.SetStateAction<ServiceFreeReportState>>;
	total: Total;
	setTotal: React.Dispatch<React.SetStateAction<Total>>;
}

export const BreedingContext = createContext<BreedingContextValue | null>(null);

export const BreedingProvider: React.FC<BreedingContextProps> = ({
	children,
	serviceFeeReportState: initialServiceFeeReportState,
}) => {
	const [rowSelection, setRowSelection] = useState<MRT_RowSelectionState>({});
	const [list, setList] = useState<Breeding[]>([]);
	const [serviceFeeReportState, setServiceFeeReportState] = useState<ServiceFreeReportState>(
		initialServiceFeeReportState ?? {}
	);
	const [total, setTotal] = useState<Total>({
		serviceFeeReport: 0,
	});

	const contextValue = useMemo(
		() => ({
			children,
			rowSelection,
			list,
			setRowSelection,
			setList,
			serviceFeeReportState,
			setServiceFeeReportState,
			total,
			setTotal,
		}),
		[children, rowSelection, list, serviceFeeReportState, total]
	);

	return <BreedingContext.Provider value={contextValue}>{children}</BreedingContext.Provider>;
};

export const useBreedingContext = () => {
	const context = use(BreedingContext);

	if (!context) {
		throw new Error('useBreedingContext must be used within a BreedingContext');
	}

	return context;
};
