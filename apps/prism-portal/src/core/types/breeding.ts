import { type BaseListRequest } from './common';

import {
	type Contact,
	type Media,
	type MediaAttachment,
	type OwnerCategory,
	type OwnershipList,
	type ServiceFeeChargeTo,
} from '@/core';
import { type Option } from '@/core/types/schedule';

export interface OwnerBreeding {
	account: {
		firstName: string;
		id: number;
		lastLogin: Nullable<number>;
		lastName: string;
		name: string;
	};
	addressLine1?: Nullable<string>;
	addressLine2?: Nullable<string>;
	adminViewStatus: number;
	company: Nullable<string>;
	contact: Contact;
	dob: Nullable<string>;
	email: Nullable<string>;
	emails: Nullable<string[]>;
	firstName: string;
	fullName: string;
	id: number;
	lastName: string;
	mobile: string;
	name: string;
	managingOwnerEmails?: string;
}

export interface BreedingOwner {
	abn: Nullable<string>;
	amount: number;
	discount: number;
	gstRegistered: Nullable<string>;
	id: number;
	managingOwner: boolean;
	owner: OwnerBreeding;
	ownerCategory?: OwnerCategory;
	ownerShare: number;
	ownership: Nullable<OwnershipList>;
}

export interface BreedingTasks {
	actualAppointment: Nullable<string>;
	actualTime: Nullable<string>;
	actualTimeSection: Nullable<string>;
	assignee: Nullable<string>;
	attachments: Media[];
	bookingTime: Nullable<string>;
	cervix: Nullable<string>;
	comment: Nullable<string>;
	dayTime: Nullable<string>;
	expenseIds: number[];
	fluid: Nullable<string>;
	id: number;
	leftOvary: Nullable<string>;
	name: Nullable<string>;
	plannedTime: number;
	plannedTimeSection: Nullable<string>;
	previousTaskNotCompleted: boolean;
	reExamineString: string;
	result: Nullable<string>;
	rightOvary: Nullable<string>;
	service: Nullable<string>;
	serviceId: Nullable<string>;
	session: number;
	treatmentTasks: [];
	type: number;
	uterus: Nullable<string>;
	vulva: Nullable<string>;
}

interface Location {
	id: number;
	name: string;
	notificationStatus: Nullable<string>;
	removable: Nullable<string>;
}

export interface Mare {
	category: Nullable<string>;
	countryCode: Nullable<string>;
	dam: Nullable<string>;
	id: number;
	location?: Location;
	name: string;
	sire: string;
	trainer?: Option;
	yearOfBirth: number;
}

export interface Stallion {
	countryCode: Nullable<string>;
	dam: Nullable<string>;
	id: number;
	name: Nullable<string>;
	sire: Nullable<string>;
	trainer?: Option;
	yearOfBirth: Nullable<number>;
}

export interface LatestResult {
	actualAppointment: Nullable<string>;
	actualTime: number;
	actualTimeSection: Nullable<string>;
	assignee: Nullable<string>;
	attachments: Media[];
	bookingTime: Nullable<number>;
	cervix: Nullable<string>;
	comment: Nullable<string>;
	dayTime: Nullable<string>;
	expenseIds: number[];
	fluid: Nullable<string>;
	id: number;
	leftOvary: string;
	name: string;
	plannedTime: number;
	plannedTimeSection: Nullable<string>;
	previousTaskNotCompleted: boolean;
	reExamineString: Nullable<string>;
	result: Nullable<string>;
	rightOvary: Nullable<string>;
	service: Nullable<string>;
	serviceId: Nullable<number>;
	session: number;
	treatmentTasks: string[];
	type: number;
	uterus: Nullable<string>;
	vulva: Nullable<string>;
}

export interface Agent {
	abn: Nullable<number>;
	address: Nullable<string>;
	contactPerson: string;
	country: Nullable<string>;
	email: string;
	gst: boolean;
	id: number;
	mobile: Nullable<string>;
	name: string;
	postcode: Nullable<string>;
	state: Nullable<string>;
	suburb: Nullable<string>;
}

export interface ContractPreview {
	url: string;
}

export interface Booking {
	agent: Nullable<Agent>;
	appointment: Nullable<string>;
	bookingDate: number;
	bookingFee: Nullable<string>;
	bookingHistory: Nullable<string>;
	bookingId: number;
	bookingTime: Nullable<string>;
	breederId: number;
	breedingOwner?: BreedingOwner[];
	breedingTasks?: BreedingTasks[];
	cancelable: boolean;
	chargeTo: ServiceFeeChargeTo;
	checkAvailableMedia: boolean;
	comment: Nullable<string>;
	commission: Nullable<number>;
	contractId: Nullable<number>;
	contractPreview: Nullable<ContractPreview>;
	country: {
		id: number;
		name: string;
		code: string;
	};
	coverDate: Nullable<number>;
	dateContractReceive: Nullable<number>;
	dateContractSent: Nullable<number>;
	dayTime: Nullable<string>;
	discount: number;
	discountedFee: Nullable<number>;
	commissionFee: Nullable<number>;
	editBookingDate: boolean;
	editBookingStatus: boolean;
	expenseCommissionIds: Nullable<number[]>;
	createdExpense?: Nullable<boolean>;
	expenseIds: Nullable<number[]>;
	expenseServiceFeeIds: Nullable<number>;
	firstDateOfService: Nullable<number>;
	foal?: Stallion;
	foalName: Nullable<string>;
	id: number;
	invoiceDate: Nullable<number>;
	lastDateOfService: Nullable<number>;
	lastStatus: Nullable<number>;
	lastUpdate: Nullable<number>;
	latestResult?: LatestResult;
	lastPayment?: Nullable<number>;
	mare?: Mare;
	managingOwnerData?: Mare;
	note: Nullable<string>;
	noBooking: boolean;
	offer: Nullable<number>;
	owner?: OwnerBreeding;
	ownerShare?: number;
	ownerShareAmount?: number;
	result: Nullable<string>;
	season: Nullable<string>;
	serveDate: number;
	served: boolean;
	serviceFee: number;
	serviceId: Nullable<number>;
	serviceStatus: Nullable<string>;
	serviceStatusValue: number;
	showServiceFee: boolean;
	signatureRequestId: Nullable<number>;
	source: Nullable<string>;
	stallion: Stallion;
	stallionOwnerCategory: OwnerCategory;
	stallionType: Nullable<string>;
	status: 'Confirmed' | 'Historical' | 'Unconfirmed';
	statusValue: Nullable<number>;
	successMessage: Nullable<string>;
	existed?: boolean;
	split: number;
	paid?: number;
	credit?: number;
	outstanding?: number;
}

export interface Breeding {
	booking?: Booking;
	total?: number;
}

export interface BreedingBookingPayload {
	agentIds?: number[];
	categoryIds?: number[];
	datesContractReceive?: number[];
	datesContractSent?: number[];
	forDashBoard?: boolean;
	locationIds?: number[];
	mareIds?: number[];
	orderBy?: string;
	orderDirection?: string;
	ownerIds?: number[];
	pageIndex?: number;
	pageSize?: number;
	seasonList?: number[];
	source?: string[];
	stallionIds?: number[];
	stallionTypes?: number[];
	statuses?: number[];
	lastStatuses?: string[];
	trainerId?: number | null;
	history?: boolean;
}

export interface BreedingFilter {
	agentList?: Option[];
	bookingDates?: number[];
	categories?: Option[];
	coverDates?: number[];
	datesContractReceive?: number[];
	datesContractSent?: number[];
	lastStatus?: { label: string; result: string; type: number }[];
	locations?: Option[];
	mareList?: Option[];
	noteList?: Option[];
	owners?: { displayName: string; id: number }[];
	results?: Option[];
	seasonList?: string[];
	source?: string[];
	stallionList?: Option[];
	statuses?: { statusName: string; status: number }[];
}

export interface BookingMedia {
	breedingBookingId: number;
	attachment: MediaAttachment[];
}

export interface BreedingBookingMediaPayload {
	breedingBookingId: number;
	mediaIds?: number[];
}

export interface DeleteBookingMediaPayload {
	mediaIds?: number;
	breederId?: number;
}

export interface DeleteBookingPayload {
	breederId: number;
	bookingId: number;
	trainerId: number;
	deleteRelated?: boolean;
}

export interface DeleteBookingResponse {
	contractIds?: number[];
	taskExpenseIds?: number[];
}

export interface SendBreedingEmailPayload {
	content: string;
	emails: string[];
	subject: string;
	trainerId?: number;
}

export interface ExportBreedingBookingPayload {
	pageIndex?: number;
	pageSize?: number;
	orderBy?: string;
	orderDirection?: string;
	stallionIds?: string;
	mareIds?: string;
	statuses?: string;
	ownerIds?: string;
	seasonList?: string;
	categoryIds?: string;
	agentIds?: string;
	source?: string;
	trainerId?: number;
	forDashBoard?: boolean;
	locationIds?: string;
	datesContractReceive?: string;
	datesContractSent?: string;
	stallionTypes?: string;
	headers?: string;
}
export interface BreedingReport {
	trainerId?: number;
	locationIds?: string[] | null;
	barnIds?: string[] | null;
	orderBy?: string;
	type: string;
	report: string;
}

export interface CommissionPayload {
	agentId: number | null;
	billTrackingOptionIds?: number[];
	bookingId: number;
	cost: number;
	description: string;
	expenseDate: number;
	invoiceMessage: string;
	payAccountId?: number | null;
	payAmountTax?: number | null;
	payTaxRateId?: number | null;
	trainerId?: number | null;
}

export interface BreedingHorsePayload {
	horseTypeIds: number[];
	includeArchive: boolean;
	keyword?: string;
	outsideHorse: boolean;
	pageIndex: number;
	pageSize: number;
	trainerId?: number | null;
	trainerOfHorseId?: number | null;
}

export interface BreedingHorse {
	age: number;
	colour?: { id: number; colour?: string };
	dam: string;
	foaled?: number;
	id: number;
	microchipNumber: Nullable<number>;
	name: string;
	racingColours: Nullable<string>;
	serviceFee: Nullable<number>;
	sex: string;
	sexString: string;
	sire: string;
	trainer: { name: string; id: number };
}

export interface Appointment {
	breederId: number;
	id: number;
	name: string;
	position: number;
}

export interface BreedingServiceFee {
	trainerId?: number | null;
	stallionId: number;
	timeSlotId?: number;
	date?: string;
}

export interface HorseCategoryBreedingPayload {
	trainerId?: number | null;
	horseId: number;
}

export interface MareOwnerships {
	id?: number | null;
	amount?: number | null;
	originAmount?: number | null;
	discount?: number | null;
	ownerCategoryId?: string | null;
	ownerId?: string | null;
	ownerName?: string | null;
	managingOwner?: boolean | null;
	ownerShare?: number | null;
	payOptions?: number[] | null;
}

interface MareOwnershipPayload extends Omit<MareOwnerships, 'ownerCategoryId' | 'ownerId'> {
	ownerCategoryId?: number | null;
	ownerId?: number | null;
}

export interface AddBookingPayload {
	agentId?: number | null;
	bookingDate?: number;
	bookingTime?: string | null;
	breederId?: number | null;
	chargeTo?: number;
	commission?: number;
	countryId?: number;
	coverDate?: number | null;
	dateContractReceive?: number | null;
	dateContractSent?: number | null;
	dayTime?: string | null;
	discount?: number;
	discountedFee?: number;
	id?: number;
	mareId?: number | null;
	note?: string;
	ownerBookings?: MareOwnershipPayload[];
	season?: number;
	serviceFee?: number;
	split?: number;
	stallionCategoryId?: number | null;
	stallionId?: number | null;
	status?: number;
	trainerId?: number | null;
	force?: boolean;
}

export interface BreedingTaskType {
	id: number;
	name: string;
	type: number;
}

export interface BreedingTaskPayload {
	bookingId: number;
	trainerId?: number;
	breederId?: number;
	comment: string;
	type: number;
	cervix?: string;
	vulva?: string;
	leftOvary?: string;
	rightOvary?: string;
	uterus?: string;
	fluid?: string;
	assignee: string | null;
	actualTime?: number;
	actualAppointment?: string;
	plannedTime: number;
	plannedTimeSection?: string;
	result?: string;
	season: string;
	treatmentTaskIds?: number[];
	dayTime?: string;
	crossCover?: boolean;
	name?: string;
}

export interface AbortBookingPayload {
	breederId: number;
	trainerId: number;
	actualTime: number;
	comment?: string;
	result: string;
	id: number;
	status: number;
}

export interface BreedingServiceFeeReport {
	bookings: Booking[];
	totalInvoice: number;
	totalPaid: number;
	totalCredit: number;
	totalOutstanding: number;
}

export interface BreedingServiceFeeReportPayload extends BaseListRequest {
	trainerId?: number | null;
	stallionIds?: number[];
	mareIds?: number[];
	ownerIds?: number[];
	seasons?: number[];
}

export interface BreedingServiceFeeReportFilter {
	mares: Option[];
	owners: OwnerBreeding[];
	seasons: string[];
	stallions: Option[];
}
