import { useMutation, useQuery } from '@tanstack/react-query';

import { getStaffList } from '../fetchers';
import { getBreedingTaskType } from '../fetchers/breeding';

import {
	getBookingMedia,
	getBredingAppointment,
	getBredingReport,
	getBredingReportView,
	getBreedingFilter,
	getBreedingServiceFeeReportFilter,
} from '@/api';
import { type BreedingBookingMediaPayload, QueryKey, type TrainerPayload } from '@/core';

export const useGetBreedingTaskType = (trainerId?: number, enabled?: boolean) =>
	useQuery({
		queryKey: [QueryKey.BreedingTaskType, trainerId],
		queryFn: () => getBreedingTaskType(trainerId),
		enabled,
	});
export const useGetStaffs = (trainerId?: number, enabled = true) => {
	return useQuery({
		queryFn: () => getStaffList({ trainerId }),
		queryKey: [QueryKey.StaffList, trainerId],
		refetchOnWindowFocus: false,
		enabled,
	});
};

export const useGetBreedingFilter = (payload?: TrainerPayload, enabled?: boolean) =>
	useQuery({
		queryFn: () => getBreedingFilter(payload),
		queryKey: [QueryKey.FilterBreeding, payload],
		refetchOnWindowFocus: false,
		enabled,
	});

export const useGetBookingMedia = (payload?: BreedingBookingMediaPayload, enabled?: boolean) =>
	useQuery({
		queryFn: () => getBookingMedia(payload),
		queryKey: [QueryKey.FilterBreeding, payload],
		refetchOnWindowFocus: false,
		enabled,
	});

export const useGetReportView = () =>
	useMutation({
		mutationFn: getBredingReportView,
	});

export const useGetReport = () =>
	useMutation({
		mutationFn: getBredingReport,
	});

export const useBreedingAppointment = (params: { trainerId?: number | null }) =>
	useQuery({
		queryFn: () => getBredingAppointment(params),
		queryKey: [QueryKey.BreedingAppointment, params],
		refetchOnWindowFocus: false,
	});

export const useGetBreedingServiceFeeReportFilter = (params: { trainerId?: number }) =>
	useQuery({
		queryFn: () => getBreedingServiceFeeReportFilter(params),
		queryKey: [QueryKey.BreedingServiceFeeReportFilter, params],
		refetchOnWindowFocus: false,
	});
