'use client';

import { Box } from '@mantine/core';
import { useSelectedLayoutSegment } from 'next/navigation';

import { BreedingServiceFeeReportHeaderFilter } from '@/components/breeding/breeding-service-fee-report/breeding-service-fee-report-header-filter';

export const BreedingHeaderFilter = () => {
	const segment = useSelectedLayoutSegment();

	return <Box className='px-6 pt-4'>{segment === 'service-fee-report' && <BreedingServiceFeeReportHeaderFilter />}</Box>;
};
