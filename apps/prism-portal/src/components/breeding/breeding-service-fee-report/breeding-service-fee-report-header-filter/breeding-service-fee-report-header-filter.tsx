import { Card, Text } from '@mantine/core';
import { useTranslations } from 'next-intl';

import { useBreedingContext } from '@/core';

const TotalLabel = ({ label, value, color }: { label: string; value: number; color: string }) => {
	const formatPattern = /\d(?=(?:\d{3})+\.)/g;

	return (
		<Text
			className='text-foreground-secondary'
			component='span'
			fw={700}
			fz='xs'
			tt='uppercase'
		>
			{label}: &nbsp;&nbsp;
			<Text
				c={color}
				component='span'
				fw={700}
				fz='xl'
			>
				${value.toFixed(2).replace(formatPattern, '$&,')}
			</Text>
		</Text>
	);
};

export const BreedingServiceFeeReportHeaderFilter = () => {
	const t = useTranslations('labels.breeding.serviceFeeReport');

	const { serviceFeeReportState } = useBreedingContext();

	return (
		<Card
			className='flex-row justify-end gap-6'
			display='flex'
			padding='md'
			radius='sm'
			withBorder
		>
			<TotalLabel
				color='blue'
				label={t('totalInvoice')}
				value={serviceFeeReportState.data?.totalInvoice ?? 0}
			/>
			<TotalLabel
				color='green'
				label={t('totalPaid')}
				value={serviceFeeReportState.data?.totalPaid ?? 0}
			/>
			<TotalLabel
				color='gray.3'
				label={t('totalCredit')}
				value={serviceFeeReportState.data?.totalCredit ?? 0}
			/>
			<TotalLabel
				color='red'
				label={t('totalOutstanding')}
				value={serviceFeeReportState.data?.totalOutstanding ?? 0}
			/>
		</Card>
	);
};
