'use client';

import { useMemo } from 'react';
import { type DataTableProps } from '@glasshouse/components';
import dayjs from 'dayjs';
import { useTranslations } from 'next-intl';

import {
	renderCurrency,
	renderLinkOwnerProfile,
	renderMare,
	renderStallion,
	renderTextContent,
} from './breeding-service-fee-report-table-columns';

import { useServiceFeeReportData } from '@/components/breeding/hooks/use-service-fee-report-data';
import { DataTable } from '@/components/core';
import { type Booking, DATE_TIME_FORMAT, PAGE_INDEX_DEFAULT, PAGE_SIZE_DEFAULT } from '@/core';

export const BreedingServiceFeeReportTable = () => {
	const t = useTranslations('labels.breeding.serviceFeeReport');
	const {
		trainerId,
		filterData,
		filterValues,
		setFilterValues,
		sorting,
		setSorting,
		setTotal,
		dataQueryKey,
		onGetBreedingServiceFeeReportData,
		setServiceFeeReportState,
	} = useServiceFeeReportData();

	const columns: DataTableProps<Booking>['columns'] = useMemo(
		() => [
			{
				header: t('table.season'),
				accessorKey: 'season',
				enableSorting: true,
				enableColumnFilter: true,
				filterFn: 'season',
				filterVariant: 'multi-select',
				mantineFilterMultiSelectProps: {
					data: filterData.seasons,
				},
				Cell: ({ row }) => renderTextContent(row.original.season),
				size: 110,
			},
			{
				header: t('table.stallion'),
				accessorKey: 'stallion',
				enableSorting: true,
				enableColumnFilter: true,
				filterFn: 'stallion',
				filterVariant: 'multi-select',
				mantineFilterMultiSelectProps: {
					data: filterData.stallions,
				},
				size: 120,
				Cell: ({
					row: {
						original: { stallion },
					},
				}) => renderStallion(stallion.name, stallion.id, trainerId),
			},
			{
				header: t('table.owner'),
				accessorKey: 'owner',
				enableSorting: true,
				enableColumnFilter: true,
				filterFn: 'owner',
				filterVariant: 'multi-select',
				mantineFilterMultiSelectProps: {
					data: filterData.owners,
				},
				size: 120,
				Cell: ({
					row: {
						original: { owner },
					},
				}) => renderLinkOwnerProfile(owner?.name, owner?.id),
			},
			{
				header: t('table.mare'),
				accessorKey: 'mare',
				enableSorting: true,
				enableColumnFilter: true,
				filterFn: 'mare',
				filterVariant: 'multi-select',
				mantineFilterMultiSelectProps: {
					data: filterData.mares,
				},
				size: 120,
				Cell: ({
					row: {
						original: { mare },
					},
				}) => renderMare(mare, trainerId),
			},
			{
				header: '%',
				accessorKey: 'subject',
				enableSorting: false,
				enableColumnFilter: false,
				size: 80,
				Cell: ({ row: { original } }) => renderTextContent(original.ownerShare?.toString(), '%'),
			},
			{
				header: t('table.serviceFee'),
				accessorKey: 'serviceFee',
				enableSorting: true,
				enableColumnFilter: false,
				size: 110,
				Cell: ({ row: { original } }) => renderCurrency(original.serviceFee),
			},
			{
				header: t('table.invoiceDate'),
				accessorKey: 'invoiceDate',
				enableSorting: false,
				enableColumnFilter: false,
				size: 110,
				Cell: ({ row: { original } }) =>
					renderTextContent(original.invoiceDate ? dayjs(original.invoiceDate).format(DATE_TIME_FORMAT) : undefined),
			},
			{
				header: t('table.totalInvoice'),
				accessorKey: 'totalInvoice',
				enableSorting: false,
				enableColumnFilter: false,
				size: 110,
				Cell: ({ row: { original } }) => renderCurrency(original.ownerShareAmount),
			},
			{
				header: t('table.paid'),
				accessorKey: 'paid',
				enableSorting: false,
				enableColumnFilter: false,
				size: 110,
				Cell: ({ row: { original } }) => renderCurrency(original.paid),
			},
			{
				header: t('table.credit'),
				accessorKey: 'credit',
				enableSorting: false,
				enableColumnFilter: false,
				size: 110,
				Cell: ({ row: { original } }) => renderCurrency(original.credit),
			},
			{
				header: t('table.outstanding'),
				accessorKey: 'outstanding',
				enableSorting: false,
				enableColumnFilter: false,
				size: 110,
				Cell: ({ row: { original } }) => renderCurrency(original.outstanding),
			},
			{
				header: t('table.lastPayment'),
				accessorKey: 'lastPayment',
				enableSorting: false,
				enableColumnFilter: false,
				size: 110,
				Cell: ({ row: { original } }) =>
					renderTextContent(original.lastPayment ? dayjs(original.lastPayment).format(DATE_TIME_FORMAT) : undefined),
			},
		],
		[filterList, t, trainerId]
	);

	const handleFiltersChange = (columnFilters) => {
		setFilterValues(columnFilters);
	};

	return (
		<DataTable
			columnResizeMode='onChange'
			columns={columns}
			enableBottomToolbar={false}
			enableColumnFilterModes={false}
			enableColumnOrdering={false}
			enableColumnResizing
			enableFilters
			enableSorting
			enableSortingRemoval={false}
			enableStickyHeader
			enableTableActionsColumn={false}
			getData={({ pageParam }) => onGetBreedingServiceFeeReportData(pageParam.pageIndex, pageParam.pageSize)}
			infinite
			layoutMode='grid'
			manualSorting
			onColumnFiltersChange={handleFiltersChange}
			onSortingChange={setSorting}
			state={{ columnFilters: filterValues, sorting }}
			mantinePaperProps={{
				mx: '8px',
			}}
			mantineTableContainerProps={{
				w: '100%',
			}}
			mantineTableHeadCellProps={{
				className: 'bg-white',
			}}
			onDataFetch={data => {
				const latestData = data?.pages[data.pages.length - 1]?.responseData;

				setTotal(prev => ({
					...prev,
					serviceFeeReport: latestData?.bookings.length ?? 0,
				}));

				setServiceFeeReportState(prev => ({
					...prev,
					data: {
						totalInvoice: latestData?.totalInvoice ?? 0,
						totalPaid: latestData?.totalPaid ?? 0,
						totalCredit: latestData?.totalCredit ?? 0,
						totalOutstanding: latestData?.totalOutstanding ?? 0,
					},
				}));
			}}
			queryOptions={{
				queryKey: dataQueryKey,
				initialPageParam: { pageSize: PAGE_SIZE_DEFAULT, pageIndex: PAGE_INDEX_DEFAULT },
				select: ({ pageParams, pages }) => ({
					pageParams,
					pages: pages.map(page => page?.responseData?.bookings ?? []),
				}),
				getNextPageParam: (lastPage, _, lastPageParams) => {
					const nextPageParam =
						lastPage?.responseData && lastPage.responseData.bookings.length === PAGE_SIZE_DEFAULT
							? { pageIndex: lastPageParams.pageIndex + 1, pageSize: PAGE_SIZE_DEFAULT }
							: null;

					return nextPageParam;
				},
				refetchOnWindowFocus: false,
				staleTime: 0,
			}}
		/>
	);
};
