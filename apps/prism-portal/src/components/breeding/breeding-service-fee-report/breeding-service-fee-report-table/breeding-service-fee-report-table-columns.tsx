import { Anchor, Text } from '@mantine/core';
import Link from 'next/link';

import { type Mare } from '@/core';

export const renderLink = ({
	url,
	label,
	variant,
	onClick,
}: {
	url?: string;
	label?: string | null;
	variant?: string;
	onClick?: VoidFunction;
}) => (
	<Anchor
		component={Link}
		href={url ?? '#'}
		onClick={onClick}
		target='_blank'
		variant={variant ?? 'default'}
	>
		<Text className='whitespace-pre-line text-wrap hover:underline'>{label}</Text>
	</Anchor>
);

export const renderTextContent = (textContent?: string | null, suffix?: string) => {
	if (!textContent) return null;

	return (
		<Text
			className='max-w-60 whitespace-pre-line text-wrap'
			truncate='end'
		>
			{textContent}
			{suffix}
		</Text>
	);
};

export const renderCurrency = (value?: number) => {
	const num = value ?? 0;
	const formatPattern = /\d(?=(?:\d{3})+\.)/g;

	return (
		<Text
			className='max-w-60 whitespace-pre-line text-wrap'
			truncate='end'
		>
			${num.toFixed(2).replace(formatPattern, '$&,')}
		</Text>
	);
};

export const renderStallion = (name: string | null, horseId: number | null, trainerId?: number) => {
	return renderLink({
		url: `/portal/horse/${trainerId ?? 'all'}/${horseId}/profile`,
		label: name,
	});
};

export const renderMare = (mare?: Mare, trainerId?: number) => {
	if (trainerId !== mare?.trainer?.id) return renderTextContent(mare?.name);

	return renderLink({
		url: `/portal/horse/${mare?.trainer?.id ?? 'all'}/${mare?.id}/profile`,
		label: mare?.name,
	});
};

export const renderLinkOwnerProfile = (name?: string, ownerId?: number) => {
	return renderLink({
		url: `/portal/owner/all/${ownerId}/accountId/profile`,
		label: name,
		variant: 'trainer',
	});
};
