import { useMemo, useState } from 'react';
import { type SortingState } from '@tanstack/react-table';
import { isNil, omitBy,orderBy as _orderBy } from 'lodash';
import { type MRT_ColumnFiltersState } from 'mantine-react-table';

import { getBreedingServiceFeeReport, useGetBreedingServiceFeeReportFilter } from '@/api';
import { QueryKey, useBreedingContext, useTrainerContext } from '@/core';
import { type Option } from '@/core/types/schedule';

export const useServiceFeeReportData = () => {
	const { trainerId } = useTrainerContext();
	const {
		serviceFeeReportState: { filters: serviceFeeReportFilter },
		setServiceFeeReportState,
		setTotal,
	} = useBreedingContext();
	const { orderBy, orderDirection, stallionIds, mareIds, ownerIds, seasons } = serviceFeeReportFilter ?? {};
	const [columnFilters, setColumnFilters] = useState<MRT_ColumnFiltersState>([
		{ id: 'season', value: seasons ?? [] },
		{ id: 'stallion', value: stallionIds ?? [] },
		{ id: 'mare', value: mareIds ?? [] },
		{ id: 'owner', value: ownerIds ?? [] },
	]);
	const [sorting, setSorting] = useState<SortingState>([
		{
			id: orderBy ?? 'season',
			desc: orderDirection ? orderDirection === 'desc' : true,
		},
	]);

	const { data: dataFilterList } = useGetBreedingServiceFeeReportFilter({ trainerId });

	const mapOption = (o: Option) => ({
		label: o.name,
		value: o.id.toString(),
	});
	const filterData = useMemo(
		() => ({
			seasons: _orderBy(dataFilterList?.responseData?.seasons ?? [], [], ['desc']),
			stallions: (dataFilterList?.responseData?.stallions ?? []).map(mapOption),
			owners: (dataFilterList?.responseData?.owners ?? []).map(mapOption),
			mares: (dataFilterList?.responseData?.mares ?? []).map(mapOption),
		}),
		[dataFilterList]
	);

	const sanitizedPayload = useMemo(() => {
		const payload = {
			...(serviceFeeReportFilter ?? {}),
			trainerId,
			orderBy: sorting[0]?.id,
			orderDirection: sorting[0]?.desc ? 'desc' : 'asc',
		};

		return omitBy(payload, isNil);
	}, [serviceFeeReportFilter, trainerId, sorting]);

	const queryKey = [QueryKey.CommunicationReportByHorse, sanitizedPayload];
	const getBreedingServiceFeeReportData = (pageIndex: number, pageSize: number) => {
		return getBreedingServiceFeeReport({
			...sanitizedPayload,
			pageIndex,
			pageSize,
		});
	};

	const updateFilters = (
		newFilters: Partial<{
			stallionIds: string[];
			mareIds: string[];
			ownerIds: string[];
			seasons: string[];
		}>
	) => {
		const updatedColumnFilters = columnFilters.map(filter => {
			switch (filter.id) {
				case 'stallion':
					return { ...filter, value: newFilters.stallionIds ?? filter.value };
				case 'mare':
					return { ...filter, value: newFilters.mareIds ?? filter.value };
				case 'owner':
					return { ...filter, value: newFilters.ownerIds ?? filter.value };
				case 'season':
					return { ...filter, value: newFilters.seasons ?? filter.value };
				default:
					return filter;
			}
		});

		setColumnFilters(updatedColumnFilters);
		setServiceFeeReportState(prev => ({
			...prev,
			filters: {
				...prev.filters,
				...newFilters,
			},
		}));
	};

	return {
		filterData,
		filterValues: columnFilters,
    setColumnFilters,
		updateFilters,

		sorting,
		setSorting,
		trainerId,
		setTotal,
		dataQueryKey: queryKey,
		serviceFeeReportFilter,

		onGetBreedingServiceFeeReportData: getBreedingServiceFeeReportData,
		setServiceFeeReportState,
	};
};
