import { useMemo, useState } from 'react';
import { type SortingState } from '@tanstack/react-table';
import { isNil, omitBy } from 'lodash';
import { type MRT_ColumnFiltersState } from 'mantine-react-table';

import { getBreedingServiceFeeReport, useGetBreedingServiceFeeReportFilter } from '@/api';
import { QueryKey, useBreedingContext, useTrainerContext } from '@/core';
import { type Option } from '@/core/types/schedule';

export const useServiceFeeReportData = () => {
	const { trainerId } = useTrainerContext();
	const {
		serviceFeeReportState: { filter: serviceFeeReportFilter },
		setServiceFeeReportState,
		setTotal,
	} = useBreedingContext();
	const { orderBy, orderDirection, stallionIds, mareIds, ownerIds, seasons } = serviceFeeReportFilter ?? {};
	const [columnFilters, setColumnFilters] = useState<MRT_ColumnFiltersState>([
		{ id: 'season', value: seasons ?? [] },
		{ id: 'stallion', value: stallionIds ?? [] },
		{ id: 'mare', value: mareIds ?? [] },
		{ id: 'owner', value: ownerIds ?? [] },
	]);
	const [sorting, setSorting] = useState<SortingState>([
		{
			id: orderBy ?? 'season',
			desc: orderDirection ? orderDirection === 'desc' : true,
		},
	]);

	const { data: dataFilterList } = useGetBreedingServiceFeeReportFilter({ trainerId });

	const mapOption = (o: Option) => ({
		label: o.name,
		value: o.id.toString(),
	});
	const filterData = useMemo(
		() => ({
			seasons: dataFilterList?.responseData?.seasons ?? [],
			stallions: (dataFilterList?.responseData?.stallions ?? []).map(mapOption),
			owners: (dataFilterList?.responseData?.owners ?? []).map(mapOption),
			mares: (dataFilterList?.responseData?.mares ?? []).map(mapOption),
		}),
		[dataFilterList]
	);

	const sanitizedPayload = useMemo(() => {
		const payload = {
			...(serviceFeeReportFilter ?? {}),
			trainerId,
			orderBy: sorting[0]?.id,
			orderDirection: sorting[0]?.desc ? 'desc' : 'asc',
		};

		return omitBy(payload, isNil);
	}, [serviceFeeReportFilter, trainerId, sorting]);

	const queryKey = [QueryKey.CommunicationReportByHorse, sanitizedPayload];
	const getBreedingServiceFeeReportData = (pageIndex: number, pageSize: number) => {
		return getBreedingServiceFeeReport({
			...sanitizedPayload,
			pageIndex,
			pageSize,
		});
	};

	return {
		filterData,
		filterValues: columnFilters,
		setFilterValues: setColumnFilters,

		sorting,
		setSorting,
		trainerId,
		setTotal,
		dataQueryKey: queryKey,

		onGetBreedingServiceFeeReportData: getBreedingServiceFeeReportData,
		setServiceFeeReportState,
	};
};
